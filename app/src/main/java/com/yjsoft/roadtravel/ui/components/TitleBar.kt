package com.yjsoft.roadtravel.ui.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.ui.theme.RoadTravelTheme

/**
 * 标题栏对齐方式
 */
enum class TitleAlignment {
    CENTER,     // 居中对齐
    LEFT        // 左对齐（在左侧图标右侧）
}

/**
 * 图标尺寸数据类
 *
 * @param width 图标宽度
 * @param height 图标高度
 */
data class IconSize(
    val width: Dp,
    val height: Dp
) {
    constructor(size: Dp) : this(size, size)
}

/**
 * 右侧图标按钮数据类
 *
 * @param imageVector ImageVector图标，优先使用
 * @param drawableRes Drawable资源ID，当imageVector为null时使用
 * @param contentDescription 无障碍描述
 * @param onClick 点击事件
 * @param size 图标尺寸，默认为24dp
 */
data class RightIconButton(
    val imageVector: ImageVector? = null,
    @DrawableRes val drawableRes: Int? = null,
    val contentDescription: String = "Right Icon",
    val onClick: (() -> Unit)? = null,
    val size: IconSize = IconSize(24.dp)
)

/**
 * 自定义标题栏组件
 *
 * @param title 标题文本
 * @param titleAlignment 标题对齐方式
 * @param leftIcon 左侧图标（ImageVector类型）
 * @param leftIconRes 左侧图标（Drawable资源ID）- 当leftIcon为null时使用
 * @param rightIcons 右侧图标按钮列表，支持多个图标
 * @param titleLeftIcon 标题左侧图标（ImageVector类型）
 * @param titleRightIcon 标题右侧图标（ImageVector类型，仅在居中对齐时显示）
 * @param titleLeftIconRes 标题左侧图标（Drawable资源ID）- 当titleLeftIcon为null时使用
 * @param titleRightIconRes 标题右侧图标（Drawable资源ID）- 当titleRightIcon为null时使用
 * @param onLeftIconClick 左侧图标点击事件
 * @param onTitleLeftIconClick 标题左侧图标点击事件
 * @param onTitleRightIconClick 标题右侧图标点击事件
 * @param backgroundColor 背景颜色
 * @param contentColor 内容颜色
 * @param titleIconColor 标题图标颜色，为null时使用contentColor
 * @param titleIconSpacing 标题图标与标题文本的间距
 * @param leftIconSize 左侧图标尺寸
 * @param titleLeftIconSize 标题左侧图标尺寸
 * @param titleRightIconSize 标题右侧图标尺寸（仅在居中对齐时显示）
 * @param rightIconSpacing 右侧图标之间的间距
 * @param includeStatusBarPadding 是否包含状态栏间距，默认为true
 * @param modifier 修饰符
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TitleBar(
    title: String,
    titleAlignment: TitleAlignment = TitleAlignment.CENTER,
    leftIcon: ImageVector? = null,
    @DrawableRes leftIconRes: Int? = null,
    rightIcons: List<RightIconButton> = emptyList(),
    titleLeftIcon: ImageVector? = null,
    titleRightIcon: ImageVector? = null,
    @DrawableRes titleLeftIconRes: Int? = null,
    @DrawableRes titleRightIconRes: Int? = null,
    onLeftIconClick: (() -> Unit)? = null,
    onTitleLeftIconClick: (() -> Unit)? = null,
    onTitleRightIconClick: (() -> Unit)? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = MaterialTheme.colorScheme.onSurface,
    titleIconColor: Color? = null,
    titleIconSpacing: Dp = 4.dp,
    leftIconSize: IconSize = IconSize(24.dp),
    titleLeftIconSize: IconSize = IconSize(20.dp),
    titleRightIconSize: IconSize = IconSize(20.dp),
    rightIconSpacing: Dp = 2.dp,
    includeStatusBarPadding: Boolean = true,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .then(if (includeStatusBarPadding) Modifier.statusBarsPadding() else Modifier),
        color = backgroundColor,
        contentColor = contentColor
    ) {
        when (titleAlignment) {
            TitleAlignment.CENTER -> {
                CenterAlignedTitleBar(
                    title = title,
                    leftIcon = leftIcon,
                    rightIcons = rightIcons,
                    titleLeftIcon = titleLeftIcon,
                    titleRightIcon = titleRightIcon,
                    leftIconRes = leftIconRes,
                    titleLeftIconRes = titleLeftIconRes,
                    titleRightIconRes = titleRightIconRes,
                    onLeftIconClick = onLeftIconClick,
                    onTitleLeftIconClick = onTitleLeftIconClick,
                    onTitleRightIconClick = onTitleRightIconClick,
                    contentColor = contentColor,
                    titleIconColor = titleIconColor,
                    titleIconSpacing = titleIconSpacing,
                    leftIconSize = leftIconSize,
                    titleLeftIconSize = titleLeftIconSize,
                    titleRightIconSize = titleRightIconSize,
                    rightIconSpacing = rightIconSpacing
                )
            }

            TitleAlignment.LEFT -> {
                LeftAlignedTitleBar(
                    title = title,
                    leftIcon = leftIcon,
                    rightIcons = rightIcons,
                    titleLeftIcon = titleLeftIcon,
                    leftIconRes = leftIconRes,
                    titleLeftIconRes = titleLeftIconRes,
                    onLeftIconClick = onLeftIconClick,
                    onTitleLeftIconClick = onTitleLeftIconClick,
                    contentColor = contentColor,
                    titleIconColor = titleIconColor,
                    titleIconSpacing = titleIconSpacing,
                    leftIconSize = leftIconSize,
                    titleLeftIconSize = titleLeftIconSize,
                    rightIconSpacing = rightIconSpacing
                )
            }
        }
    }
}

/**
 * 统一的图标渲染组件
 *
 * @param imageVector ImageVector图标，优先使用
 * @param drawableRes Drawable资源ID，当imageVector为null时使用
 * @param contentDescription 无障碍描述
 * @param tint 图标颜色
 * @param iconSize 图标尺寸，为null时使用modifier默认尺寸
 * @param modifier 修饰符
 */
@Composable
private fun IconRenderer(
    imageVector: ImageVector?,
    @DrawableRes drawableRes: Int?,
    contentDescription: String,
    tint: Color,
    iconSize: IconSize? = null,
    modifier: Modifier = Modifier
) {
    val finalModifier = if (iconSize != null) {
        modifier.size(iconSize.width, iconSize.height)
    } else {
        modifier
    }

    when {
        imageVector != null -> {
            Icon(
                imageVector = imageVector,
                contentDescription = contentDescription,
                tint = tint,
                modifier = finalModifier
            )
        }

        drawableRes != null -> {
            Icon(
                painter = painterResource(id = drawableRes),
                contentDescription = contentDescription,
                tint = tint,
                modifier = finalModifier
            )
        }
    }
}

/**
 * 居中对齐的标题栏
 */
@Composable
private fun CenterAlignedTitleBar(
    title: String,
    leftIcon: ImageVector?,
    rightIcons: List<RightIconButton>,
    titleLeftIcon: ImageVector?,
    titleRightIcon: ImageVector?,
    @DrawableRes leftIconRes: Int?,
    @DrawableRes titleLeftIconRes: Int?,
    @DrawableRes titleRightIconRes: Int?,
    onLeftIconClick: (() -> Unit)?,
    onTitleLeftIconClick: (() -> Unit)?,
    onTitleRightIconClick: (() -> Unit)?,
    contentColor: Color,
    titleIconColor: Color?,
    titleIconSpacing: Dp,
    leftIconSize: IconSize,
    titleLeftIconSize: IconSize,
    titleRightIconSize: IconSize,
    rightIconSpacing: Dp
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        // 左侧图标
        if (leftIcon != null || leftIconRes != null) {
            IconButton(
                onClick = { onLeftIconClick?.invoke() },
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .size(leftIconSize.width + 8.dp, leftIconSize.height + 8.dp)
            ) {
                IconRenderer(
                    imageVector = leftIcon,
                    drawableRes = leftIconRes,
                    contentDescription = "Left Icon",
                    tint = contentColor,
                    iconSize = leftIconSize
                )
            }
        }

        // 居中的标题和标题左右侧图标 - 重新设计为紧凑布局
        Row(
            modifier = Modifier
                .align(Alignment.Center)
                .wrapContentWidth()
                .widthIn(max = 280.dp), // 设置最大宽度以处理长文本
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            // 标题左侧图标
            if (titleLeftIcon != null || titleLeftIconRes != null) {
                if (onTitleLeftIconClick != null) {
                    // 有点击事件时使用IconButton
                    IconButton(
                        onClick = onTitleLeftIconClick,
                        modifier = Modifier.size(titleLeftIconSize.width + 4.dp)
                    ) {
                        IconRenderer(
                            imageVector = titleLeftIcon,
                            drawableRes = titleLeftIconRes,
                            contentDescription = "Title Left Icon",
                            tint = titleIconColor ?: contentColor,
                            iconSize = titleLeftIconSize
                        )
                    }
                } else {
                    // 无点击事件时直接使用Icon
                    IconRenderer(
                        imageVector = titleLeftIcon,
                        drawableRes = titleLeftIconRes,
                        contentDescription = "Title Left Icon",
                        tint = titleIconColor ?: contentColor,
                        iconSize = titleLeftIconSize
                    )
                }
                Spacer(modifier = Modifier.width(titleIconSpacing))
            }

            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = contentColor,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
                modifier = Modifier.wrapContentWidth() // 只占用实际需要的宽度，让图标紧贴文本
            )

            // 标题右侧图标
            if (titleRightIcon != null || titleRightIconRes != null) {
                Spacer(modifier = Modifier.width(titleIconSpacing))
                if (onTitleRightIconClick != null) {
                    // 有点击事件时使用IconButton
                    IconButton(
                        onClick = onTitleRightIconClick,
                        modifier = Modifier.size(
                            titleRightIconSize.width + 4.dp,
                            titleRightIconSize.height + 4.dp
                        )
                    ) {
                        IconRenderer(
                            imageVector = titleRightIcon,
                            drawableRes = titleRightIconRes,
                            contentDescription = "Title Right Icon",
                            tint = titleIconColor ?: contentColor,
                            iconSize = titleRightIconSize
                        )
                    }
                } else {
                    // 无点击事件时直接使用Icon
                    IconRenderer(
                        imageVector = titleRightIcon,
                        drawableRes = titleRightIconRes,
                        contentDescription = "Title Right Icon",
                        tint = titleIconColor ?: contentColor,
                        iconSize = titleRightIconSize
                    )
                }
            }
        }

        // 右侧图标组
        if (rightIcons.isNotEmpty()) {
            Row(
                modifier = Modifier.align(Alignment.CenterEnd),
                horizontalArrangement = Arrangement.spacedBy(rightIconSpacing),
                verticalAlignment = Alignment.CenterVertically
            ) {
                rightIcons.forEach { iconButton ->
                    if (iconButton.onClick != null) {
                        // 有点击事件时使用紧凑的IconButton
                        IconButton(
                            onClick = iconButton.onClick,
                            modifier = Modifier.size(
                                iconButton.size.width + 8.dp,
                                iconButton.size.height + 8.dp
                            )
                        ) {
                            IconRenderer(
                                imageVector = iconButton.imageVector,
                                drawableRes = iconButton.drawableRes,
                                contentDescription = iconButton.contentDescription,
                                tint = contentColor,
                                iconSize = iconButton.size
                            )
                        }
                    } else {
                        // 无点击事件时直接使用Icon，更紧凑
                        IconRenderer(
                            imageVector = iconButton.imageVector,
                            drawableRes = iconButton.drawableRes,
                            contentDescription = iconButton.contentDescription,
                            tint = contentColor,
                            iconSize = iconButton.size,
                            modifier = Modifier.padding(4.dp) // 添加少量内边距保持视觉平衡
                        )
                    }
                }
            }
        }
    }
}

/**
 * 左对齐的标题栏
 */
@Composable
private fun LeftAlignedTitleBar(
    title: String,
    leftIcon: ImageVector?,
    rightIcons: List<RightIconButton>,
    titleLeftIcon: ImageVector?,
    @DrawableRes leftIconRes: Int?,
    @DrawableRes titleLeftIconRes: Int?,
    onLeftIconClick: (() -> Unit)?,
    onTitleLeftIconClick: (() -> Unit)?,
    contentColor: Color,
    titleIconColor: Color?,
    titleIconSpacing: Dp,
    leftIconSize: IconSize,
    titleLeftIconSize: IconSize,
    rightIconSpacing: Dp
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧图标
        if (leftIcon != null || leftIconRes != null) {
            IconButton(
                onClick = { onLeftIconClick?.invoke() },
                modifier = Modifier.size(leftIconSize.width + 8.dp, leftIconSize.height + 8.dp)
            ) {
                IconRenderer(
                    imageVector = leftIcon,
                    drawableRes = leftIconRes,
                    contentDescription = "Left Icon",
                    tint = contentColor,
                    iconSize = leftIconSize
                )
            }
        }

        // 标题和标题左侧图标 - 重新设计为紧凑布局
        Row(
            modifier = Modifier
                .weight(1f)
                .padding(start = if (leftIcon == null) 20.dp else 0.dp, end = 8.dp), // 右侧添加一些间距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 标题内容组（图标+文本）作为一个整体
            Row(
                modifier = Modifier
                    .wrapContentWidth()
                    .widthIn(max = 240.dp), // 设置最大宽度以处理长文本
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 标题左侧图标
                if (titleLeftIcon != null || titleLeftIconRes != null) {
                    if (onTitleLeftIconClick != null) {
                        // 有点击事件时使用IconButton
                        IconButton(
                            onClick = onTitleLeftIconClick,
                            modifier = Modifier.size(
                                titleLeftIconSize.width + 4.dp,
                                titleLeftIconSize.height + 4.dp
                            )
                        ) {
                            IconRenderer(
                                imageVector = titleLeftIcon,
                                drawableRes = titleLeftIconRes,
                                contentDescription = "Title Left Icon",
                                tint = titleIconColor ?: contentColor,
                                iconSize = titleLeftIconSize
                            )
                        }
                    } else {
                        // 无点击事件时直接使用Icon
                        IconRenderer(
                            imageVector = titleLeftIcon,
                            drawableRes = titleLeftIconRes,
                            contentDescription = "Title Left Icon",
                            tint = titleIconColor ?: contentColor,
                            iconSize = titleLeftIconSize
                        )
                    }
                    Spacer(modifier = Modifier.width(titleIconSpacing))
                }

                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = contentColor,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.wrapContentWidth() // 只占用实际需要的宽度，让图标紧贴文本
                )
            }
        }

        // 右侧图标组
        if (rightIcons.isNotEmpty()) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(rightIconSpacing),
                verticalAlignment = Alignment.CenterVertically
            ) {
                rightIcons.forEach { iconButton ->
                    if (iconButton.onClick != null) {
                        // 有点击事件时使用紧凑的IconButton
                        IconButton(
                            onClick = iconButton.onClick,
                            modifier = Modifier.size(
                                iconButton.size.width + 8.dp,
                                iconButton.size.height + 8.dp
                            )
                        ) {
                            IconRenderer(
                                imageVector = iconButton.imageVector,
                                drawableRes = iconButton.drawableRes,
                                contentDescription = iconButton.contentDescription,
                                tint = contentColor,
                                iconSize = iconButton.size
                            )
                        }
                    } else {
                        // 无点击事件时直接使用Icon，更紧凑
                        IconRenderer(
                            imageVector = iconButton.imageVector,
                            drawableRes = iconButton.drawableRes,
                            contentDescription = iconButton.contentDescription,
                            tint = contentColor,
                            iconSize = iconButton.size,
                            modifier = Modifier.padding(4.dp) // 添加少量内边距保持视觉平衡
                        )
                    }
                }
            }
        } else {
            Spacer(modifier = Modifier.width(48.dp)) // 保持平衡
        }
    }
}

/**
 * 标题文本长度限制工具函数
 */
object TitleUtils {

    /**
     * 限制标题长度，超出部分用省略号替代
     * @param title 原始标题
     * @param maxLength 最大长度，默认20个字符
     * @return 处理后的标题
     */
    fun limitTitleLength(title: String, maxLength: Int = 20): String {
        return if (title.length > maxLength) {
            "${title.take(maxLength)}..."
        } else {
            title
        }
    }

    /**
     * 智能标题限制，根据字符类型动态调整长度
     * 中文字符占用更多显示空间
     * @param title 原始标题
     * @param maxChineseLength 中文字符最大长度，默认12个字符
     * @param maxEnglishLength 英文字符最大长度，默认24个字符
     * @return 处理后的标题
     */
    fun smartLimitTitle(
        title: String,
        maxChineseLength: Int = 12,
        maxEnglishLength: Int = 24
    ): String {
        if (title.isEmpty()) return title

        // 检测主要是中文还是英文
        val chineseCharCount = title.count { it.code > 127 }
        val totalCharCount = title.length
        val isMainlyChinese = chineseCharCount > totalCharCount / 2

        val maxLength = if (isMainlyChinese) maxChineseLength else maxEnglishLength

        return if (title.length > maxLength) {
            "${title.take(maxLength)}..."
        } else {
            title
        }
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
fun TitleBarPreview() {
    RoadTravelTheme {
        Column {
            // 居中对齐，带标题左右侧图标和多个右侧图标
            TitleBar(
                title = "居中标题",
                titleAlignment = TitleAlignment.CENTER,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.Star,
                        contentDescription = "收藏",
                        onClick = { }
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { }
                    )
                ),
                titleLeftIcon = Icons.Default.Star,
                titleRightIcon = Icons.Default.Home,
                onLeftIconClick = { },
                onTitleLeftIconClick = { },
                onTitleRightIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 左对齐，带标题左侧图标和多个右侧图标
            TitleBar(
                title = "左对齐标题",
                titleAlignment = TitleAlignment.LEFT,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.Star,
                        contentDescription = "收藏",
                        onClick = { }
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { }
                    )
                ),
                titleLeftIcon = Icons.Default.Star,
                onLeftIconClick = { },
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 只有标题
            TitleBar(
                title = "只有标题",
                titleAlignment = TitleAlignment.CENTER
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 只有标题左侧图标
            TitleBar(
                title = "带左侧图标",
                titleAlignment = TitleAlignment.CENTER,
                titleLeftIcon = Icons.Default.Star,
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 测试长标题 - 居中对齐
            TitleBar(
                title = "这是一个非常非常长的网页标题，用来测试文本溢出处理效果",
                titleAlignment = TitleAlignment.CENTER,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { }
                    )
                ),
                onLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 测试长标题 - 左对齐
            TitleBar(
                title = "这是一个非常非常长的网页标题，用来测试左对齐模式下的文本溢出处理效果",
                titleAlignment = TitleAlignment.LEFT,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { }
                    )
                ),
                titleLeftIcon = Icons.Default.Star,
                onLeftIconClick = { },
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 使用drawable资源 - 支付相关图标
            TitleBar(
                title = "支付方式",
                titleAlignment = TitleAlignment.CENTER,
                leftIconRes = R.drawable.ic_payment_alipay,
                rightIcons = listOf(
                    RightIconButton(
                        drawableRes = R.drawable.ic_payment_wechat,
                        contentDescription = "微信支付",
                        onClick = { }
                    )
                ),
                titleLeftIconRes = R.drawable.ic_payment_unionpay,
                onLeftIconClick = { },
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 混合使用ImageVector和drawable资源，多个右侧图标
            TitleBar(
                title = "混合图标类型",
                titleAlignment = TitleAlignment.LEFT,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack, // ImageVector
                rightIcons = listOf(
                    RightIconButton(
                        drawableRes = R.drawable.ic_payment_wechat, // Drawable资源
                        contentDescription = "微信支付",
                        onClick = { }
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.Star, // ImageVector
                        contentDescription = "收藏",
                        onClick = { }
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert, // ImageVector
                        contentDescription = "更多",
                        onClick = { }
                    )
                ),
                titleLeftIconRes = R.drawable.ic_payment_alipay, // Drawable资源
                onLeftIconClick = { },
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 自定义标题图标尺寸 - 左侧图标较小，右侧图标较大
            TitleBar(
                title = "自定义图标尺寸",
                titleAlignment = TitleAlignment.CENTER,
                titleLeftIcon = Icons.Default.Star,
                titleRightIcon = Icons.Default.Home,
                titleLeftIconSize = IconSize(16.dp),  // 左侧图标较小
                titleRightIconSize = IconSize(28.dp), // 右侧图标较大
                onTitleLeftIconClick = { },
                onTitleRightIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 左对齐模式的大尺寸标题图标和紧凑的右侧图标
            TitleBar(
                title = "大尺寸标题图标",
                titleAlignment = TitleAlignment.LEFT,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                leftIconSize = IconSize(28.dp), // 自定义左侧图标尺寸
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.Star,
                        contentDescription = "收藏",
                        onClick = { },
                        size = IconSize(20.dp)
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { },
                        size = IconSize(20.dp)
                    )
                ),
                titleLeftIconRes = R.drawable.ic_payment_unionpay,
                titleLeftIconSize = IconSize(32.dp), // 大尺寸标题图标
                rightIconSpacing = 1.dp, // 紧凑间距
                onLeftIconClick = { },
                onTitleLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 演示不同尺寸的左侧图标
            TitleBar(
                title = "自定义左侧图标尺寸",
                titleAlignment = TitleAlignment.CENTER,
                leftIconRes = R.drawable.ic_payment_alipay,
                leftIconSize = IconSize(width = 20.dp, height = 32.dp), // 不同宽高的左侧图标
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.Star,
                        contentDescription = "收藏",
                        onClick = { },
                        size = IconSize(18.dp)
                    )
                ),
                rightIconSpacing = 0.dp,
                onLeftIconClick = { }
            )

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 演示不同尺寸的右侧图标和超紧凑间距
            TitleBar(
                title = "多尺寸右侧图标",
                titleAlignment = TitleAlignment.CENTER,
                leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
                rightIcons = listOf(
                    RightIconButton(
                        imageVector = Icons.Default.Star,
                        contentDescription = "收藏",
                        onClick = { },
                        size = IconSize(18.dp) // 小图标
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.Home,
                        contentDescription = "首页",
                        onClick = { },
                        size = IconSize(24.dp) // 中等图标
                    ),
                    RightIconButton(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多",
                        onClick = { },
                        size = IconSize(20.dp) // 标准图标
                    )
                ),
                rightIconSpacing = 0.dp, // 超紧凑间距
                onLeftIconClick = { }
            )
        }
    }
}

/**
 * TitleBar 使用示例和最佳实践
 */
object TitleBarExamples {

    /**
     * 创建一个带有自定义返回按钮和多个右侧图标的标题栏
     */
    @Composable
    fun CustomBackButtonWithMultipleRightIcons() {
        TitleBar(
            title = "自定义标题栏",
            titleAlignment = TitleAlignment.CENTER,
            // 自定义返回按钮图片和尺寸
            leftIconRes = R.drawable.ic_payment_alipay, // 使用自定义图片作为返回按钮
            leftIconSize = IconSize(width = 24.dp, height = 28.dp), // 自定义左侧图标尺寸
            // 多个右侧图标，间距紧凑
            rightIcons = listOf(
                RightIconButton(
                    imageVector = Icons.Default.Star,
                    contentDescription = "收藏",
                    onClick = { /* 收藏逻辑 */ },
                    size = IconSize(20.dp)
                ),
                RightIconButton(
                    drawableRes = R.drawable.ic_payment_wechat,
                    contentDescription = "分享",
                    onClick = { /* 分享逻辑 */ },
                    size = IconSize(22.dp)
                ),
                RightIconButton(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多选项",
                    onClick = { /* 更多选项逻辑 */ },
                    size = IconSize(20.dp)
                )
            ),
            rightIconSpacing = 1.dp, // 紧凑间距
            onLeftIconClick = { /* 自定义返回逻辑 */ }
        )
    }

    /**
     * 创建一个左对齐的标题栏，支持不同尺寸的图标
     */
    @Composable
    fun LeftAlignedWithCustomSizes() {
        TitleBar(
            title = "左对齐标题",
            titleAlignment = TitleAlignment.LEFT,
            leftIcon = Icons.AutoMirrored.Filled.ArrowBack,
            leftIconSize = IconSize(width = 26.dp, height = 22.dp), // 自定义左侧图标尺寸
            rightIcons = listOf(
                RightIconButton(
                    imageVector = Icons.Default.Star,
                    contentDescription = "收藏",
                    onClick = { },
                    size = IconSize(width = 18.dp, height = 24.dp) // 不同宽高
                ),
                RightIconButton(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多",
                    onClick = { },
                    size = IconSize(20.dp) // 正方形
                )
            ),
            titleLeftIcon = Icons.Default.Home,
            titleLeftIconSize = IconSize(width = 16.dp, height = 20.dp), // 标题图标也支持不同宽高
            rightIconSpacing = 0.dp, // 超紧凑
            onLeftIconClick = { },
            onTitleLeftIconClick = { }
        )
    }
}

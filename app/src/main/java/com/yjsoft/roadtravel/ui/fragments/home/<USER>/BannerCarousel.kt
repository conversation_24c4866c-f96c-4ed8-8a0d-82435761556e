package com.yjsoft.roadtravel.ui.fragments.home.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImage
import com.yjsoft.roadtravel.ui.fragments.home.Banner
import kotlinx.coroutines.delay

/**
 * Banner轮播组件
 * 
 * 特性：
 * - 高度100dp，圆角40dp
 * - 自动轮播功能
 * - 页面指示器
 * - 支持点击事件
 * 
 * @param banners Banner数据列表
 * @param onBannerClick Banner点击事件回调
 * @param modifier 修饰符
 * @param autoScrollDuration 自动滚动间隔时间（毫秒）
 */
@Composable
fun BannerCarousel(
    modifier: Modifier = Modifier,
    banners: List<Banner>,
    onBannerClick: (Banner) -> Unit = {},
    autoScrollDuration: Long = 3000L
) {
    if (banners.isEmpty()) return
    
    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { banners.size }
    )
    
    // 自动轮播逻辑
    LaunchedEffect(pagerState.currentPage) {
        if (banners.size > 1) {
            delay(autoScrollDuration)
            val nextPage = (pagerState.currentPage + 1) % banners.size
            pagerState.animateScrollToPage(nextPage)
        }
    }
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 轮播容器
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.Transparent),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val banner = banners[page]
                
                NetworkImage(
                    url = banner.pic,
                    contentDescription = banner.title,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(12.dp))
                        .clickable { onBannerClick(banner) },
                    contentScale = ContentScale.Crop
                )
            }
        }
        
        // 页面指示器
        if (banners.size > 1) {
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(banners.size) { index ->
                    val isSelected = index == pagerState.currentPage
                    Box(
                        modifier = Modifier
                            .size(if (isSelected) 8.dp else 6.dp)
                            .background(
                                color = if (isSelected) Color(0xFF1990FF) else Color(0xFFE0E0E0),
                                shape = CircleShape
                            )
                    )
                }
            }
        }
    }
}

/**
 * 简化版Banner轮播组件（无指示器）
 */
@Composable
fun SimpleBannerCarousel(
    modifier: Modifier = Modifier,
    banners: List<Banner>,
    onBannerClick: (Banner) -> Unit = {},
    autoScrollDuration: Long = 3000L
) {
    if (banners.isEmpty()) return
    
    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { banners.size }
    )
    
    // 自动轮播逻辑
    LaunchedEffect(pagerState.currentPage) {
        if (banners.size > 1) {
            delay(autoScrollDuration)
            val nextPage = (pagerState.currentPage + 1) % banners.size
            pagerState.animateScrollToPage(nextPage)
        }
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(100.dp),
        shape = RoundedCornerShape(40.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val banner = banners[page]
            
            NetworkImage(
                url = banner.pic,
                contentDescription = banner.title,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(40.dp))
                    .clickable { onBannerClick(banner) },
                contentScale = ContentScale.Crop
            )
        }
    }
}

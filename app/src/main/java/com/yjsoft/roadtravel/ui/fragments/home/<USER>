package com.yjsoft.roadtravel.ui.fragments.home

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 首页ViewModel
 * 
 * 功能：
 * - 管理首页状态
 * - 处理定位功能
 * - 处理各种功能测试
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context
) : BaseViewModel<UiState<HomeUiState>>() {
    
    companion object {
        private const val TAG = "HomeViewModel %s"
    }
    
    // 初始化状态标志
    private var isDataInitialized = false
    
    // 网络管理器和API服务
    private val apiService = NetworkManager.getApiService(context)
    
    // 数据存储仓库
    private val dataStoreRepository = DataStoreRepository.getInstance()
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): UiState<HomeUiState> {
        return UiState.Success(HomeUiState())
    }
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "HomeViewModel启动")
        initializeHome()
    }
    
    /**
     * 初始化首页
     */
    private fun initializeHome() {
        safeLaunch {
            updateState { currentUiState ->
                when (currentUiState) {
                    is UiState.Success -> {
                        UiState.Success(
                            currentUiState.data.copy(
                                isInitialized = true,
                                lastAction = "首页初始化完成"
                            )
                        )
                    }
                    else -> UiState.Success(HomeUiState(isInitialized = true, lastAction = "首页初始化完成"))
                }
            }
            LogManager.d(TAG, "首页初始化完成")
        }
    }
    
    /**
     * 检查是否需要初始化数据
     */
    fun shouldInitializeData(): Boolean {
        return !isDataInitialized
    }
    
    /**
     * 标记数据已初始化
     */
    fun markDataAsInitialized() {
        isDataInitialized = true
        LogManager.d(TAG, "数据初始化标志已设置")
    }
    
    // ========== 定位功能 ==========
    
    /**
     * 处理定位成功
     */
    fun handleLocationResult(location: LocationData) {
        safeLaunch {
            val cityName = location.city ?: location.district ?: "未知城市"
            
            // 更新UI状态
            updateState { currentUiState ->
                when (currentUiState) {
                    is UiState.Success -> {
                        UiState.Success(
                            currentUiState.data.copy(
                                cityName = cityName,
                                latitude = location.latitude,
                                longitude = location.longitude,
                                lastAction = "定位成功: $cityName"
                            )
                        )
                    }
                    else -> UiState.Success(
                        HomeUiState(
                            cityName = cityName,
                            latitude = location.latitude,
                            longitude = location.longitude,
                            lastAction = "定位成功: $cityName"
                        )
                    )
                }
            }
            
            // 更新初始化数据中的位置信息
            try {
                dataStoreRepository.updateInitLocationInfo(
                    zoneName = cityName,
                    lat = location.latitude,
                    lng = location.longitude
                )
                LogManager.d(TAG, "已更新初始化位置信息: $cityName")
            } catch (e: Exception) {
                LogManager.e(TAG, "更新初始化位置信息失败", e)
            }
            
            showToast("定位成功: $cityName")
            LogManager.d(TAG, "定位成功: $cityName (${location.latitude}, ${location.longitude})")
            
            // 定位成功后调用geo接口获取详细地理信息
            callGeoApi(location.latitude, location.longitude)
        }
    }
    
    /**
     * 处理定位成功（内部方法）
     */
    private fun handleLocationSuccess(location: LocationData) {
        val cityName = location.city ?: location.district ?: "未知城市"
        updateState { currentUiState ->
            when (currentUiState) {
                is UiState.Success -> {
                    UiState.Success(
                        currentUiState.data.copy(
                            cityName = cityName,
                            latitude = location.latitude,
                            longitude = location.longitude,
                            lastAction = "定位成功: $cityName"
                        )
                    )
                }
                else -> UiState.Success(
                    HomeUiState(
                        cityName = cityName,
                        latitude = location.latitude,
                        longitude = location.longitude,
                        lastAction = "定位成功: $cityName"
                    )
                )
            }
        }
        showToast("定位成功: $cityName")
        LogManager.d(TAG, "定位成功: $cityName")
    }
    
    /**
     * 处理定位错误
     */
    fun handleLocationError(error: String) {
        updateState { currentUiState ->
            when (currentUiState) {
                is UiState.Success -> {
                    UiState.Success(
                        currentUiState.data.copy(
                            lastAction = "定位失败: $error"
                        )
                    )
                }
                else -> UiState.Success(HomeUiState(lastAction = "定位失败: $error"))
            }
        }
        showError("定位失败: $error")
        LogManager.e(TAG, "定位失败: $error")
    }
    
    /**
     * 处理定位错误（异常对象）
     */
    fun handleLocationError(exception: Exception) {
        handleLocationError(exception.message ?: "未知定位错误")
    }
    
    /**
     * 获取定位配置
     */
    fun getLocationConfig(): LocationConfig {
        return LocationConfig.singleHighAccuracy()
    }
    
    // ========== 页面操作 ==========
    
    /**
     * 刷新页面
     */
    fun refresh() {
        safeLaunch {
            setLoading(true)
            try {
                // 模拟刷新操作
                kotlinx.coroutines.delay(1000)
                
                updateState { currentUiState ->
                    when (currentUiState) {
                        is UiState.Success -> {
                            UiState.Success(
                                currentUiState.data.copy(
                                    lastAction = "页面刷新完成"
                                )
                            )
                        }
                        else -> UiState.Success(HomeUiState(lastAction = "页面刷新完成"))
                    }
                }
                showToast("刷新完成")
                LogManager.d(TAG, "页面刷新完成")
            } finally {
                setLoading(false)
            }
        }
    }
    
    /**
     * 获取当前位置信息
     */
    fun getCurrentLocation(): Pair<Double, Double>? {
        return when (val state = currentState) {
            is UiState.Success -> {
                val data = state.data
                if (data.latitude != 0.0 && data.longitude != 0.0) {
                    Pair(data.latitude, data.longitude)
                } else null
            }
            else -> null
        }
    }
    
    /**
     * 加载初始化城市名称
     */
    fun loadInitCityName() {
        safeLaunch {
            try {
                val initCityName = dataStoreRepository.getInitZoneName()
                updateState { currentUiState ->
                    when (currentUiState) {
                        is UiState.Success -> {
                            UiState.Success(
                                currentUiState.data.copy(
                                    cityName = initCityName,
                                    lastAction = "加载初始化城市: $initCityName"
                                )
                            )
                        }
                        else -> UiState.Success(
                            HomeUiState(
                                cityName = initCityName,
                                lastAction = "加载初始化城市: $initCityName"
                            )
                        )
                    }
                }
                LogManager.d(TAG, "已加载初始化城市名称: $initCityName")
            } catch (e: Exception) {
                LogManager.e(TAG, "加载初始化城市名称失败", e)
                // 如果加载失败，保持默认的"上海市"
            }
        }
    }
    
    /**
     * 更新城市名称
     */
    fun updateCityName(cityName: String) {
        safeLaunch {
            updateState { currentUiState ->
                when (currentUiState) {
                    is UiState.Success -> {
                        UiState.Success(
                            currentUiState.data.copy(
                                cityName = cityName,
                                lastAction = "城市更新为: $cityName"
                            )
                        )
                    }
                    else -> UiState.Success(
                        HomeUiState(
                            cityName = cityName,
                            lastAction = "城市更新为: $cityName"
                        )
                    )
                }
            }
            LogManager.d(TAG, "城市名称已更新为: $cityName")
        }
    }
    
    /**
     * 加载首页数据
     */
    fun loadHomeData() {
        safeLaunch {
            LogManager.d(TAG, "开始加载首页数据")
            setLoading(true)
            
            try {
                // 调用首页接口（公共参数由拦截器自动添加）
                val response = apiService.getHomeIndex()
                
                if (response.isSuccess()) {
                    val homeData = response.getDataOrThrow()
                    updateState { currentUiState ->
                        when (currentUiState) {
                            is UiState.Success -> {
                                UiState.Success(
                                    currentUiState.data.copy(
                                        homeData = homeData,
                                        lastAction = "首页数据加载成功"
                                    )
                                )
                            }
                            else -> UiState.Success(
                                HomeUiState(
                                    homeData = homeData,
                                    lastAction = "首页数据加载成功"
                                )
                            )
                        }
                    }
                    LogManager.d(TAG, "首页数据加载成功")
                    showToast("首页数据加载成功")
                    
                    // 首页数据加载成功后，如果有热门标签，自动加载第一个标签的计划数据
                    if (homeData.hotTags.isNotEmpty()) {
                        LogManager.d(TAG, "首页数据中存在热门标签，开始加载默认计划数据")
                        loadPlansData()
                    }
                } else {
                    val errorMsg = response.msg
                    updateState { currentUiState ->
                        when (currentUiState) {
                            is UiState.Success -> {
                                UiState.Success(
                                    currentUiState.data.copy(
                                        lastAction = "首页数据加载失败: $errorMsg"
                                    )
                                )
                            }
                            else -> UiState.Success(
                                HomeUiState(lastAction = "首页数据加载失败: $errorMsg")
                            )
                        }
                    }
                    LogManager.e(TAG, "首页数据加载失败: $errorMsg")
                    showError("首页数据加载失败: $errorMsg")
                }
            } catch (e: Exception) {
                val errorMsg = e.message ?: "未知错误"
                updateState { currentUiState ->
                    when (currentUiState) {
                        is UiState.Success -> {
                            UiState.Success(
                                currentUiState.data.copy(
                                    lastAction = "首页数据加载异常: $errorMsg"
                                )
                            )
                        }
                        else -> UiState.Success(
                            HomeUiState(lastAction = "首页数据加载异常: $errorMsg")
                        )
                    }
                }
                LogManager.e(TAG, "首页数据加载异常", e)
                showError("首页数据加载失败: $errorMsg")
            } finally {
                setLoading(false)
            }
        }
    }
    
    /**
     * 选择热门标签
     */
    fun selectTag(index: Int) {
        safeLaunch {
            updateState { currentUiState ->
                when (currentUiState) {
                    is UiState.Success -> {
                        UiState.Success(
                            currentUiState.data.copy(
                                selectedTagIndex = index,
                                lastAction = "选择标签: 索引$index"
                            )
                        )
                    }
                    else -> currentUiState
                }
            }
            LogManager.d(TAG, "选择了标签索引: $index")
            
            // 选择标签后加载对应的计划数据
            loadPlansData()
        }
    }

    /**
     * 加载计划数据
     */
    fun loadPlansData() {
        safeLaunch {
            when (val currentUiState = currentState) {
                is UiState.Success -> {
                    val homeData = currentUiState.data.homeData
                    val hotTags = homeData?.hotTags
                    val selectedIndex = currentUiState.data.selectedTagIndex
                    // 优先使用状态中的zoneId，如果为0则使用DataStore中的初始值
                    val zoneId = if (currentUiState.data.zoneId > 0) {
                        currentUiState.data.zoneId
                    } else {
                        try {
                            dataStoreRepository.getInitZoneId()
                        } catch (e: Exception) {
                            LogManager.e(TAG, "获取zone_id失败", e)
                            0
                        }
                    }
                    
                    if (hotTags != null && selectedIndex < hotTags.size && zoneId > 0) {
                        val selectedTag = hotTags[selectedIndex]
                        LogManager.d(TAG, "开始加载计划数据，zone_id: $zoneId, hot_tag: $selectedTag")
                        
                        try {
                            val response = apiService.getPlans(selectedTag)
                            
                            if (response.isSuccess()) {
                                val plansData = response.getDataOrThrow()
                                updateState { currentState ->
                                    when (currentState) {
                                        is UiState.Success -> {
                                            UiState.Success(
                                                currentState.data.copy(
                                                    plansData = plansData,
                                                    lastAction = "计划数据加载成功: ${plansData.list.size}条"
                                                )
                                            )
                                        }
                                        else -> currentState
                                    }
                                }
                                LogManager.d(TAG, "计划数据加载成功，共${plansData.list.size}条数据")
                            } else {
                                val errorMsg = response.msg
                                updateState { currentState ->
                                    when (currentState) {
                                        is UiState.Success -> {
                                            UiState.Success(
                                                currentState.data.copy(
                                                    lastAction = "计划数据加载失败: $errorMsg"
                                                )
                                            )
                                        }
                                        else -> currentState
                                    }
                                }
                                LogManager.e(TAG, "计划数据加载失败: $errorMsg")
                                showError("计划数据加载失败: $errorMsg")
                            }
                        } catch (e: Exception) {
                            val errorMsg = e.message ?: "未知错误"
                            updateState { currentState ->
                                when (currentState) {
                                    is UiState.Success -> {
                                        UiState.Success(
                                            currentState.data.copy(
                                                lastAction = "计划数据加载异常: $errorMsg"
                                            )
                                        )
                                    }
                                    else -> currentState
                                }
                            }
                            LogManager.e(TAG, "计划数据加载异常", e)
                            showError("计划数据加载失败: $errorMsg")
                        }
                    } else {
                        LogManager.w(TAG, "无法加载计划数据，参数不足: hotTags=${hotTags?.size}, selectedIndex=$selectedIndex, zoneId=$zoneId")
                    }
                }
                else -> {
                    LogManager.w(TAG, "当前状态不支持加载计划数据")
                }
            }
        }
    }

    /**
     * 调用地理信息接口
     */
    fun callGeoApi(latitude: Double, longitude: Double) {
        safeLaunch {
            LogManager.d(TAG, "开始调用地理信息接口: ($latitude, $longitude)")
            
            try {
                val response = apiService.getGeoInfo(latitude, longitude)
                
                if (response.isSuccess()) {
                    val geoData = response.getDataOrThrow()
                    
                    // 保存地理信息数据到DataStore
                    dataStoreRepository.updateGeoInfo(geoData)
                    
                    // 更新UI状态中的地理信息（包含完整的地理数据）
                    updateState { currentUiState ->
                        when (currentUiState) {
                            is UiState.Success -> {
                                UiState.Success(
                                    currentUiState.data.copy(
                                        cityName = geoData.city,
                                        latitude = geoData.lat,
                                        longitude = geoData.lng,
                                        address = geoData.address,
                                        zoneId = geoData.zoneId,
                                        lastAction = "地理信息更新: ${geoData.city}"
                                    )
                                )
                            }
                            else -> UiState.Success(
                                HomeUiState(
                                    cityName = geoData.city,
                                    latitude = geoData.lat,
                                    longitude = geoData.lng,
                                    address = geoData.address,
                                    zoneId = geoData.zoneId,
                                    lastAction = "地理信息更新: ${geoData.city}"
                                )
                            )
                        }
                    }
                    
                    LogManager.d(TAG, "地理信息接口调用成功: ${geoData.city} - ${geoData.address} (区域ID: ${geoData.zoneId})")
                    
                    // 检查zone_id是否发生变化，如果变化则重新加载首页数据
                    try {
                        val currentInitZoneId = dataStoreRepository.getInitZoneId()
                        if (geoData.zoneId != currentInitZoneId) {
                            LogManager.d(TAG, "检测到zone_id变化: $currentInitZoneId -> ${geoData.zoneId}，重新加载首页数据")
                            
                            // 更新初始化zone_id为新值
                            dataStoreRepository.setValue(
                                CommonPreferenceKeys.INIT_ZONE_ID,
                                geoData.zoneId
                            )
                            
                            // 重新调用首页接口（公共参数由拦截器自动添加）
                            val homeResponse = apiService.getHomeIndex()
                            if (homeResponse.isSuccess()) {
                                val newHomeData = homeResponse.getDataOrThrow()
                                updateState { currentUiState ->
                                    when (currentUiState) {
                                        is UiState.Success -> {
                                            UiState.Success(
                                                currentUiState.data.copy(
                                                    homeData = newHomeData,
                                                    lastAction = "首页数据已根据新位置重新加载: ${geoData.city}"
                                                )
                                            )
                                        }
                                        else -> currentUiState
                                    }
                                }
                                LogManager.d(TAG, "使用新zone_id重新加载首页数据成功: ${geoData.zoneId}")
                                showToast("已根据当前位置更新首页内容")
                                
                                // 重新加载首页数据后，如果有热门标签，自动加载第一个标签的计划数据
                                if (newHomeData.hotTags.isNotEmpty()) {
                                    LogManager.d(TAG, "位置变化后重新加载计划数据")
                                    loadPlansData()
                                }
                            } else {
                                LogManager.e(TAG, "使用新zone_id重新加载首页数据失败: ${homeResponse.msg}")
                                // 重新加载失败不影响地理信息更新，只记录错误
                            }
                        } else {
                            LogManager.d(TAG, "zone_id未变化($currentInitZoneId)，无需重新加载首页数据")
                        }
                    } catch (e: Exception) {
                        LogManager.e(TAG, "检查zone_id变化时发生异常，不影响地理信息更新", e)
                    }
                } else {
                    val errorMsg = response.msg
                    LogManager.e(TAG, "地理信息接口调用失败: $errorMsg")
                    // 地理信息接口失败不影响主流程，只记录日志
                }
            } catch (e: Exception) {
                val errorMsg = e.message ?: "未知错误"
                LogManager.e(TAG, "地理信息接口调用异常", e)
                // 地理信息接口异常不影响主流程，只记录日志
            }
        }
    }
}

/**
 * 首页UI状态
 */
data class HomeUiState(
    val isInitialized: Boolean = false,
    val cityName: String = "上海市",
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val address: String = "",
    val zoneId: Int = 0,
    val hasLocationPermission: Boolean? = null,
    val lastAction: String = "",
    val homeData: HomeData? = null,
    val selectedTagIndex: Int = 0,
    val plansData: PlansData? = null
)

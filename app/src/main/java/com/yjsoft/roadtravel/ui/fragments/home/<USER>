package com.yjsoft.roadtravel.ui.fragments.home

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
//import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.location.viewmodel.LocationViewModel
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseFragment
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.ui.components.IconSize
import com.yjsoft.roadtravel.ui.components.TitleBar
import com.yjsoft.roadtravel.ui.fragments.home.components.BannerCarousel
import com.yjsoft.roadtravel.ui.fragments.home.components.HotTagsList
import com.yjsoft.roadtravel.ui.fragments.home.components.PlanListCard
import com.yjsoft.roadtravel.ui.fragments.home.components.PromptsList

/**
 * 首页Fragment
 *
 * 功能：
 * - 显示首页内容
 * - 处理定位功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class HomeFragment : BaseFragment() {

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun setupContent(): @Composable () -> Unit = {
        HomeScreen()
    }
}

/**
 * 首页屏幕
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = hiltViewModel(),
    locationViewModel: LocationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val locationState by locationViewModel.locationState.collectAsStateWithLifecycle()
    val locationData by locationViewModel.locationData.collectAsStateWithLifecycle()
//    val coroutineScope = rememberCoroutineScope()

    // 自动开始定位和加载首页数据（仅首次）
    LaunchedEffect(Unit) {
        if (viewModel.shouldInitializeData()) {
            LogManager.d("HomeScreen %s", "首次进入，开始初始化")

            // 先加载初始化城市名作为默认显示
            viewModel.loadInitCityName()

            // 初始化定位服务
            locationViewModel.initializeLocationService()
            // 请求位置权限并开始定位
            locationViewModel.requestLocationAndStart(
                config = viewModel.getLocationConfig(),
                includeBackgroundLocation = false
            )

            // 加载首页数据
            viewModel.loadHomeData()

            // 兜底机制：10秒后自动标记为已初始化，避免因权限问题导致永远不初始化
            kotlinx.coroutines.delay(10000)
            if (viewModel.shouldInitializeData()) {
                LogManager.d("HomeScreen %s", "定位超时，使用兜底机制标记已初始化")
                viewModel.markDataAsInitialized()
            }
        } else {
            LogManager.d("HomeScreen %s", "数据已初始化，跳过重复操作")
        }
    }

    // 监听定位结果（仅在初始化时）
    LaunchedEffect(locationData) {
        // 只有在数据未初始化时才处理定位结果（首次定位）
        if (!viewModel.shouldInitializeData()) {
            return@LaunchedEffect
        }

        locationData?.let { location ->
            if (location.isValid) {
                val cityName = location.city ?: location.district ?: location.province ?: "未知城市"
                LogManager.d("HomeScreen首次定位成功，城市：$cityName")
                viewModel.updateCityName(cityName)
                viewModel.handleLocationResult(location)

                // 定位成功后标记为已初始化
                viewModel.markDataAsInitialized()
            }
        }
    }

    // 监听定位错误（仅在初始化时）
    LaunchedEffect(locationState) {
        // 只有在数据未初始化时才处理定位错误（首次定位）
        if (!viewModel.shouldInitializeData()) {
            return@LaunchedEffect
        }

        if (locationState.error != null) {
            val errorMessage = locationState.error?.message ?: "定位失败"
            LogManager.e("HomeScreen首次定位失败：$errorMessage")
            viewModel.handleLocationError(errorMessage)
            // 定位失败时使用初始化城市名称
            viewModel.loadInitCityName()

            // 定位失败后也标记为已初始化，避免重复尝试
            viewModel.markDataAsInitialized()
        }
    }

    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 固定的标题栏
        TitleBar(
            title = when (val state = uiState) {
                is UiState.Success -> state.data.cityName
                else -> "上海市"
            },
            titleLeftIconRes = R.mipmap.ic_location,
            titleRightIconRes = R.mipmap.ic_change_city,
            titleIconColor = Color(0xFF1990FF),
            titleLeftIconSize = IconSize(18.dp),
            titleRightIconSize = IconSize(12.dp),
            includeStatusBarPadding = false,
            backgroundColor = Color.Transparent
        )

        // 可滚动的内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {

            // 热门标签列表
            when (val state = uiState) {
                is UiState.Success -> {
                    state.data.homeData?.hotTags?.let { tags ->
                        if (tags.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(12.dp))
                            HotTagsList(
                                tags = tags,
                                selectedIndex = state.data.selectedTagIndex,
                                onTagSelected = { index ->
                                    viewModel.selectTag(index)
                                }
                            )

                            // 计划列表卡片
                            state.data.plansData?.let { plansData ->
                                if (plansData.list.isNotEmpty()) {
                                    Spacer(modifier = Modifier.height(16.dp))
                                    PlanListCard(
                                        cityName = state.data.cityName,
                                        selectedTag = tags[state.data.selectedTagIndex],
                                        tagColor = plansData.color,
                                        planList = plansData.list,
                                        onSeeMoreClick = {
                                            // TODO: 实现查看更多功能
                                            LogManager.d("HomeScreen %s", "点击查看更多")
                                        },
                                        modifier = Modifier.padding(horizontal = 16.dp)
                                    )
                                }
                            }
                        }
                    }
                    // Banner轮播
                    state.data.homeData?.banners?.let { banners ->
                        if (banners.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(16.dp))
                            BannerCarousel(
                                banners = banners,
                                onBannerClick = { banner ->
                                    // TODO: 实现Banner点击功能
                                    LogManager.d(
                                        "HomeScreen %s",
                                        "点击Banner: ${banner.title}, 链接: ${banner.link}"
                                    )
                                },
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }

                    // 提示列表
                    state.data.homeData?.prompts?.let { prompts ->
                        if (prompts.isNotEmpty()) {

                            Spacer(modifier = Modifier.height(16.dp))
                            PromptsList(
                                prompts = prompts,
                                onPromptClick = { prompt ->
                                    // TODO: 实现提示点击功能
                                    LogManager.d("HomeScreen %s", "点击提示: $prompt")
                                },
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }
                }

                else -> {
                    // Loading或Error状态下不显示标签
                }
            }
            // 可滚动内容区域结束
        }
    }
}


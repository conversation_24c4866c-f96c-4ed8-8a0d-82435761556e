package com.yjsoft.roadtravel.ui.fragments.home

import com.google.gson.annotations.SerializedName

/**
 * 地理信息接口响应数据模型
 */
data class GeoApiResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("data")
    val data: GeoData? = null,
    
    @SerializedName("msg")
    val msg: String
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == 0
    
    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): GeoData? = if (isSuccess()) data else null
    
    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): GeoData {
        return if (isSuccess()) {
            data ?: throw Exception("数据为空")
        } else {
            throw Exception(msg)
        }
    }
}

/**
 * 地理信息数据
 */
data class GeoData(
    @SerializedName("address")
    val address: String,
    
    @SerializedName("lng")
    val lng: Double,
    
    @SerializedName("lat")
    val lat: Double,
    
    @SerializedName("city")
    val city: String,
    
    @SerializedName("zone_id")
    val zoneId: Int
) 
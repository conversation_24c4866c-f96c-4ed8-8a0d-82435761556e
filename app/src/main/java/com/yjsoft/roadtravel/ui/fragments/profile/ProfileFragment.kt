package com.yjsoft.roadtravel.ui.fragments.profile

import android.content.Context
import android.content.ContextWrapper
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatUserManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.WeChatUserInfoData
import com.yjsoft.roadtravel.basiclibrary.image.components.CircleAvatarImageWithText
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseFragment
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants
import com.yjsoft.roadtravel.basiclibrary.navigation.core.navigateTo
import com.yjsoft.roadtravel.basiclibrary.navigation.models.NavigationParams
import com.yjsoft.roadtravel.ui.activities.main.MainActivity

/**
 * 个人中心Fragment
 *
 * 功能：
 * - 显示微信用户信息
 * - 订单状态展示
 * - 会员信息展示
 * - 功能菜单
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
class ProfileFragment : BaseFragment() {

    override fun setupContent(): @Composable () -> Unit = { ProfileScreen() }
}

/** 个人中心屏幕 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(viewModel: ProfileViewModel = viewModel()) {
    val context = LocalContext.current
    val weChatUserManager = remember { WeChatUserManager.getInstance() }

    // 使用 Flow 监听用户信息变化，实现响应式状态更新
    val userInfo by weChatUserManager.getCurrentWeChatUserFlow().collectAsState(initial = null)
    val isLoggedIn by remember(userInfo) { derivedStateOf { userInfo != null } }

    // 监听登录状态变化并记录日志
    LaunchedEffect(isLoggedIn, userInfo) {
        LogManager.d(
                "ProfileScreen",
                "登录状态变化: isLoggedIn=$isLoggedIn, userInfo=${userInfo?.nickname}"
        )
    }

    // 添加页面可见性监听，确保页面重新可见时能正确更新状态
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(lifecycleOwner) {
        lifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.RESUMED) {
            // 页面恢复时检查登录状态
            val currentLoginStatus = weChatUserManager.isWeChatLoggedIn()
            LogManager.d("ProfileScreen", "页面恢复，当前登录状态: $currentLoginStatus")
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
                modifier = Modifier.fillMaxSize().background(Color.Transparent),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 用户信息头部
                UserInfoHeader(
                        userInfo = userInfo,
                        isLoggedIn = isLoggedIn,
                        onLoginClick = {
                            // 使用封装好的路由框架跳转到登录页面
                            navigateToLogin(context)
                        }
                )
            }

            item {
                // 订单状态卡片
                OrderStatusCard()
            }

            item {
                // 会员信息卡片
                MembershipCard()
            }

            item {
                // 功能菜单
                FunctionMenuList(
                        onItemClick = { menuItem ->
                            LogManager.d("ProfileScreen", "点击菜单项: ${menuItem.title}")
                            handleMenuItemClick(context, menuItem)
                        }
                )
            }

            // 只有在已登录状态下才显示退出登录按钮
            if (isLoggedIn) {
                item {
                    // 退出登录按钮
                    LogoutButton(
                            onLogout = {
                                // 处理退出登录，Flow 会自动监听状态变化
                                viewModel.logout()
                            }
                    )
                }
            }
        }
    }
}

/** 用户信息头部 */
@Composable
fun UserInfoHeader(userInfo: WeChatUserInfoData?, isLoggedIn: Boolean, onLoginClick: () -> Unit) {
    Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
    ) {
        Row(
                modifier =
                        Modifier.fillMaxWidth().padding(20.dp).clickable {
                            if (!isLoggedIn) onLoginClick()
                        },
                verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            CircleAvatarImageWithText(
                    url = if (isLoggedIn && userInfo != null) userInfo.avatar else null,
                    name = if (isLoggedIn && userInfo != null) userInfo.nickname else "未登录",
                    contentDescription = "用户头像",
                    size = 80.dp,
                    placeholder = painterResource(id = R.drawable.avatar_placholder),
                    error = painterResource(id = R.drawable.avatar_placholder),
                    borderWidth = 2.dp,
                    borderColor = Color(0xFFE0E0E0)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                        text = if (isLoggedIn && userInfo != null) userInfo.nickname else "未登录",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                        text =
                                if (isLoggedIn && userInfo != null)
                                        "ID:${userInfo.openId.take(10)}..."
                                else "未登录",
                        fontSize = 14.sp,
                        color = Color.Gray
                )
            }
        }
    }
}

/** 订单状态卡片 */
@Composable
fun OrderStatusCard() {
    Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
    ) {
        Column(modifier = Modifier.padding(20.dp)) {
            Row(
                    modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp), // 增加左右间距
                    horizontalArrangement = Arrangement.SpaceBetween // 改为 SpaceBetween 以更好地分布空间
            ) {
                OrderStatusItem(iconRes = R.drawable.all_orders, title = "全部订单", onClick = {})
                OrderStatusItem(iconRes = R.drawable.need_pay, title = "待付款", onClick = {})
                OrderStatusItem(iconRes = R.drawable.need_go, title = "待出行", onClick = {})
                OrderStatusItem(iconRes = R.drawable.after_sales, title = "售后", onClick = {})
            }
        }
    }
}

/** 订单状态项 */
@Composable
fun OrderStatusItem(iconRes: Int, title: String, onClick: () -> Unit) {
    Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                    Modifier.clickable { onClick() }
                            .padding(horizontal = 8.dp, vertical = 4.dp) // 增加内边距，扩大点击区域
    ) {
        Image(
                painter = painterResource(id = iconRes),
                contentDescription = title,
                modifier = Modifier.size(40.dp),
                contentScale = ContentScale.Fit
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = title, fontSize = 12.sp, color = Color.Black)
    }
}

/** 会员信息卡片 */
@Composable
fun MembershipCard() {
    Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
    ) {
        Box(
                modifier =
                        Modifier.fillMaxWidth()
                                .background(
                                        brush =
                                                Brush.horizontalGradient(
                                                        colors =
                                                                listOf(
                                                                        Color(0xFF90EE90), // 浅绿色
                                                                        Color(0xFFFFFF00) // 黄色
                                                                )
                                                ),
                                        shape = RoundedCornerShape(12.dp)
                                )
                                .padding(20.dp)
        ) {
            Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
            ) {
                // 会员图标
                Image(
                        painter = painterResource(id = R.drawable.ai_travel),
                        contentDescription = "会员图标",
                        modifier = Modifier.size(50.dp),
                        contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                            text = "体验版",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(text = "有效期至：2024年8月31日", fontSize = 12.sp, color = Color.Black)
                }

                Column(horizontalAlignment = Alignment.End) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                                text = "3,234",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.Black
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                                painter = painterResource(id = R.drawable.ai_travel),
                                contentDescription = "积分图标",
                                modifier = Modifier.size(16.dp),
                                tint = Color.Blue
                        )
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                            onClick = {},
                            colors =
                                    ButtonDefaults.buttonColors(
                                            containerColor = Color.White,
                                            contentColor = Color.Black
                                    ),
                            shape = RoundedCornerShape(20.dp),
                            modifier = Modifier.height(32.dp)
                    ) { Text(text = "续费", fontSize = 12.sp) }
                }
            }
        }
    }
}

/** 功能菜单列表 */
@Composable
fun FunctionMenuList(onItemClick: (MenuItem) -> Unit) {
    val menuItems =
            listOf(
                    MenuItem(R.drawable.itinerary_collection, "行程收藏"),
                    MenuItem(R.drawable.common_information, "常用信息"),
                    MenuItem(R.drawable.air_wifi, "机上WIFI"),
                    MenuItem(R.drawable.my_activity, "我的活动"),
                    MenuItem(R.drawable.invite_code, "邀请码"),
                    MenuItem(R.drawable.wish_list, "心愿单")
            )

    Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
    ) {
        Column {
            menuItems.forEachIndexed { index, item ->
                FunctionMenuItem(item = item, onClick = { onItemClick(item) })
                if (index < menuItems.size - 1) {
                    HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 1.dp,
                            color = Color(0xFFF0F0F0)
                    )
                }
            }
        }
    }
}

/** 功能菜单项 */
@Composable
fun FunctionMenuItem(item: MenuItem, onClick: () -> Unit) {
    Row(
            modifier = Modifier.fillMaxWidth().clickable { onClick() }.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
                painter = painterResource(id = item.iconRes),
                contentDescription = item.title,
                modifier = Modifier.size(24.dp),
                contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.width(16.dp))

        Text(
                text = item.title,
                fontSize = 16.sp,
                color = Color.Black,
                modifier = Modifier.weight(1f)
        )

        Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "进入",
                tint = Color.Gray,
                modifier = Modifier.size(20.dp)
        )
    }
}

/** 退出登录按钮 */
@Composable
fun LogoutButton(onLogout: () -> Unit) {
    var showConfirmDialog by remember { mutableStateOf(false) }

    Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
    ) {
        Button(
                onClick = { showConfirmDialog = true },
                modifier = Modifier.fillMaxWidth().padding(16.dp),
                colors =
                        ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFFF4444), // 红色背景
                                contentColor = Color.White
                        ),
                shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                    text = "退出登录",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 8.dp)
            )
        }
    }

    // 确认对话框
    if (showConfirmDialog) {
        AlertDialog(
                onDismissRequest = { showConfirmDialog = false },
                title = { Text(text = "退出登录", fontSize = 18.sp, fontWeight = FontWeight.Bold) },
                text = {
                    Text(text = "确定要退出当前账户吗？退出后需要重新登录。", fontSize = 14.sp, color = Color.Gray)
                },
                confirmButton = {
                    TextButton(
                            onClick = {
                                showConfirmDialog = false
                                onLogout()
                            },
                            colors =
                                    ButtonDefaults.textButtonColors(
                                            contentColor = Color(0xFFFF4444)
                                    )
                    ) { Text(text = "确定退出", fontWeight = FontWeight.Medium) }
                },
                dismissButton = {
                    TextButton(onClick = { showConfirmDialog = false }) {
                        Text(text = "取消", color = Color.Gray)
                    }
                },
                containerColor = Color.White,
                shape = RoundedCornerShape(12.dp)
        )
    }
}

/** 处理菜单项点击 */
private fun handleMenuItemClick(context: Context, menuItem: MenuItem) {
    try {
        LogManager.d("ProfileScreen", "处理菜单项点击: ${menuItem.title}")

        // 根据菜单项标题处理不同的跳转逻辑
        when (menuItem.title) {
            "行程收藏" -> {
                // TODO: 跳转到行程收藏页面
                LogManager.d("ProfileScreen", "跳转到行程收藏页面")
            }
            "常用信息" -> {
                // TODO: 跳转到常用信息页面
                LogManager.d("ProfileScreen", "跳转到常用信息页面")
            }
            "机上WIFI" -> {
                // TODO: 跳转到机上WIFI页面
                LogManager.d("ProfileScreen", "跳转到机上WIFI页面")
            }
            "我的活动" -> {
                // TODO: 跳转到我的活动页面
                LogManager.d("ProfileScreen", "跳转到我的活动页面")
            }
            "邀请码" -> {
                // TODO: 跳转到邀请码页面
                LogManager.d("ProfileScreen", "跳转到邀请码页面")
            }
            "心愿单" -> {
                // TODO: 跳转到心愿单页面
                LogManager.d("ProfileScreen", "跳转到心愿单页面")
            }
            else -> {
                LogManager.w("ProfileScreen", "未知的菜单项: ${menuItem.title}")
            }
        }
    } catch (e: Exception) {
        LogManager.e("ProfileScreen", "处理菜单项点击失败: ${menuItem.title}", e)
    }
}

/** 导航到登录页面的辅助函数 */
private fun navigateToLogin(context: Context) {
    try {
        // 尝试获取Activity上下文
        val activity =
                when (context) {
                    is MainActivity -> context
                    is android.app.Activity -> context
                    is androidx.activity.ComponentActivity -> context
                    else -> {
                        // 如果是其他类型的Context，尝试获取Activity
                        var ctx = context
                        while (ctx is android.content.ContextWrapper) {
                            if (ctx is android.app.Activity) {
                                break
                            }
                            ctx = ctx.baseContext
                        }
                        ctx as? android.app.Activity
                    }
                }

        if (activity != null) {
            val params =
                    NavigationParams.builder()
                            .putString("from", "profile")
                            .putString("action", "login")
                            .putString("returnTo", "profile")
                            .build()

            activity.navigateTo(
                    targetClass = NavigationConstants.ActivityClasses.LOGIN,
                    params = params
            )

            LogManager.d("ProfileScreen", "使用路由框架跳转到登录页面")
        } else {
            LogManager.w("ProfileScreen", "无法获取Activity上下文，跳转失败")
        }
    } catch (e: Exception) {
        LogManager.e("ProfileScreen", "跳转到登录页面失败", e)
    }
}

/** 菜单项数据类 */
data class MenuItem(val iconRes: Int, val title: String)

package com.yjsoft.roadtravel.ui.activities.main

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import javax.inject.Inject

/**
 * Navigation events for handling different navigation scenarios
 */
sealed class NavigationEvent {
    data class NavigateToTab(val tab: BottomNavItem) : NavigationEvent()
    object ShowExitToast : NavigationEvent()
    object ExitApp : NavigationEvent()
}

/**
 * 主页ViewModel
 * 
 * 功能：
 * - 管理底部导航状态
 * - 处理定位功能
 * - 管理标题栏状态
 * - 处理页面切换逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context
) : BaseViewModel<MainUiState>() {
    
    companion object {
        private const val TAG = "MainViewModel %s"
        private const val BACK_PRESS_INTERVAL = 5000L // 5秒内双击退出
    }
    
    // 网络管理器和数据存储
    private val apiService = NetworkManager.getApiService(context)
    private val dataStoreRepository = DataStoreRepository.getInstance()

    // ========== 双击退出状态 ==========

    /**
     * 上次按返回键的时间
     */
    private var lastBackPressTime = 0L
    
    // ========== 底部导航状态 ==========
    
    /**
     * 当前选中的底部导航项
     */
    private val _selectedBottomNavItem = MutableStateFlow(BottomNavItem.HOME)
    val selectedBottomNavItem: StateFlow<BottomNavItem> = _selectedBottomNavItem.asStateFlow()
    
    /**
     * Navigation events channel for communicating with UI layer
     */
    private val _tabNavigationEvents = Channel<NavigationEvent>(Channel.BUFFERED)
    val tabNavigationEvents = _tabNavigationEvents.receiveAsFlow()
    
    /**
     * 底部导航项列表
     */
    val bottomNavItems = listOf(
        BottomNavItem.HOME,
        BottomNavItem.PROFILE
    )
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): MainUiState {
        return MainUiState()
    }
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "MainViewModel启动")
        initializeApp()
    }
    
    /**
     * 初始化应用
     */
    private fun initializeApp() {
        safeLaunch {
            // 调用初始化接口
            loadInitData()
            
            updateState { 
                it.copy(
                    lastAction = "应用初始化完成"
                )
            }
            LogManager.d(TAG, "主页应用初始化完成")
        }
    }
    
    /**
     * Emit navigation event to UI layer
     */
    private fun emitNavigationEvent(event: NavigationEvent) {
        safeLaunch {
            _tabNavigationEvents.send(event)
        }
    }
    
    /**
     * 验证导航状态一致性
     */
    private fun validateNavigationState(expectedTab: BottomNavItem): Boolean {
        val currentTab = _selectedBottomNavItem.value
        val currentPage = currentState.currentPage
        
        val isStateConsistent = currentTab == expectedTab && currentPage == expectedTab.route
        
        if (!isStateConsistent) {
            LogManager.w(TAG, "导航状态不一致 - 当前Tab: ${currentTab.title}, 期望Tab: ${expectedTab.title}, 当前页面: $currentPage")
        }
        
        return isStateConsistent
    }
    
    /**
     * 安全地更新导航状态，包含状态验证
     */
    private fun safeUpdateNavigationState(item: BottomNavItem, reason: String) {
        if (_selectedBottomNavItem.value != item) {
            val previousTab = _selectedBottomNavItem.value
            
            _selectedBottomNavItem.value = item
            
            updateState { 
                it.copy(
                    currentPage = item.route,
                    lastAction = reason
                )
            }
            
            // 验证状态更新是否成功
            if (validateNavigationState(item)) {
                LogManager.d(TAG, "导航状态更新成功: ${previousTab.title} -> ${item.title} ($reason)")
            } else {
                LogManager.e(TAG, "导航状态更新失败: ${previousTab.title} -> ${item.title} ($reason)")
            }
        }
    }
    
    /**
     * 内部状态同步方法，用于处理外部导航状态变化（不发出导航事件）
     */
    internal fun syncNavigationState(item: BottomNavItem) {
        if (_selectedBottomNavItem.value != item) {
            safeUpdateNavigationState(item, "外部导航状态同步到${item.title}页面")
        }
    }
    
    // ========== 底部导航管理 ==========
    
    /**
     * 选择底部导航项
     * 注意：此方法只更新状态，实际导航由UI层处理
     * 程序化导航（如返回键）会额外发送导航事件
     */
    fun selectBottomNavItem(item: BottomNavItem) {
        if (_selectedBottomNavItem.value != item) {
            // 使用安全的状态更新机制
            safeUpdateNavigationState(item, "用户选择${item.title}页面")
            LogManager.d(TAG, "切换到页面: ${item.title}")
        }
    }
    
    /**
     * 获取当前页面标题
     */
    fun getCurrentPageTitle(): String {
        return when (_selectedBottomNavItem.value) {
            BottomNavItem.HOME -> currentState.cityName.ifEmpty { "首页" }
            BottomNavItem.PROFILE -> "我的"
            else -> "首页" // AI_PLAN不再作为导航项，默认返回首页
        }
    }
    
    // ========== 定位功能 ==========
    
    /**
     * 处理定位结果
     */
    fun handleLocationResult(location: LocationData) {
        safeLaunch {
            val cityName = location.city ?: location.district ?: "未知城市"
            updateState {
                it.copy(
                    cityName = cityName,
                    latitude = location.latitude,
                    longitude = location.longitude,
                    lastAction = "定位成功: $cityName"
                )
            }
            LogManager.d(TAG, "定位成功: $cityName")
        }
    }

    /**
     * 处理定位错误
     */
    fun handleLocationError(error: String) {
        safeLaunch {
            updateState {
                it.copy(
                    lastAction = "定位失败: $error"
                )
            }
            showError("定位失败: $error")
            LogManager.e(TAG, "定位失败: $error")
        }
    }
    
    /**
     * 获取定位配置
     */
    fun getLocationConfig(): LocationConfig {
        return LocationConfig.singleHighAccuracy()
    }
    
    // ========== 标题栏管理 ==========
    
    /**
     * 更新标题
     */
    fun updateTitle(title: String) {
        updateState { 
            it.copy(
                customTitle = title,
                lastAction = "更新标题: $title"
            )
        }
    }
    
    /**
     * 重置标题为默认
     */
    fun resetTitle() {
        updateState { 
            it.copy(
                customTitle = "",
                lastAction = "重置标题"
            )
        }
    }
    
    // ========== 页面状态管理 ==========
    
    /**
     * 设置页面加载状态
     */
    fun setPageLoading(isLoading: Boolean) {
        updateState { 
            it.copy(
                isPageLoading = isLoading,
                lastAction = if (isLoading) "页面加载中" else "页面加载完成"
            )
        }
    }
    
    /**
     * 处理页面错误
     */
    fun handlePageError(error: String) {
        updateState { 
            it.copy(
                lastAction = "页面错误: $error"
            )
        }
        showError(error)
    }
    
    // ========== 应用状态管理 ==========
    
    /**
     * 检查是否为首页
     */
    fun isHomePage(): Boolean {
        return _selectedBottomNavItem.value == BottomNavItem.HOME
    }
    
    /**
     * 处理返回按键
     */
    fun handleBackPressed(): Boolean {
        return when (_selectedBottomNavItem.value) {
            BottomNavItem.HOME -> {
                // 在首页时，实现双击退出逻辑
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastBackPressTime < BACK_PRESS_INTERVAL) {
                    // 5秒内连续按两次，退出应用
                    LogManager.d(TAG, "用户确认退出应用")
                    emitNavigationEvent(NavigationEvent.ExitApp)
                    false // 让系统处理退出
                } else {
                    // 第一次按返回键或超过时间间隔，显示提示
                    lastBackPressTime = currentTime
                    emitNavigationEvent(NavigationEvent.ShowExitToast)
                    LogManager.d(TAG, "显示退出提示")
                    true // 不退出，等待第二次按键
                }
            }
            else -> {
                // 在其他页面时，返回首页并发送导航事件
                safeUpdateNavigationState(BottomNavItem.HOME, "返回键导航到${BottomNavItem.HOME.title}页面")
                emitNavigationEvent(NavigationEvent.NavigateToTab(BottomNavItem.HOME))
                true
            }
        }
    }
    
    /**
     * 获取页面统计信息
     */
    fun getPageStats(): String {
        return "当前页面: ${_selectedBottomNavItem.value.title}, " +
                "城市: ${currentState.cityName.ifEmpty { "未定位" }}, " +
                "初始化: ${if (currentState.isInitialized) "完成" else "进行中"}"
    }
    
    /**
     * 加载初始化数据
     */
    fun loadInitData() {
        safeLaunch {
            LogManager.d(TAG, "开始加载初始化数据")
            setLoading(true)
            
            try {
                val response = apiService.getInit()
                
                if (response.isSuccess()) {
                    val initData = response.getDataOrThrow()
                    
                    // 保存初始化数据到DataStore
                    dataStoreRepository.saveInitData(initData)
                    
                    updateState { 
                        it.copy(
                            isInitialized = true,
                            lastAction = "初始化数据加载成功"
                        )
                    }
                    
                    LogManager.d(TAG, "初始化数据加载成功: ${initData.zoneName}")
                } else {
                    val errorMsg = response.msg
                    updateState { 
                        it.copy(
                            lastAction = "初始化数据加载失败: $errorMsg"
                        )
                    }
                    LogManager.e(TAG, "初始化数据加载失败: $errorMsg")
                    showError("初始化失败: $errorMsg")
                }
            } catch (e: Exception) {
                val errorMsg = e.message ?: "未知错误"
                updateState { 
                    it.copy(
                        lastAction = "初始化数据加载异常: $errorMsg"
                    )
                }
                LogManager.e(TAG, "初始化数据加载异常", e)
                showError("初始化失败: $errorMsg")
            } finally {
                setLoading(false)
            }
        }
    }
}

/**
 * 主页UI状态
 */
data class MainUiState(
    val isInitialized: Boolean = false,
    val cityName: String = "",
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val currentPage: String = "home",
    val customTitle: String = "",
    val isPageLoading: Boolean = false,
    val lastAction: String = ""
)

/**
 * 底部导航项枚举
 */
enum class BottomNavItem(
    val route: String,
    val title: String,
    val iconRes: String // 这里使用字符串，实际使用时转换为图标
) {
    HOME("home", "首页", "home"),
    AI_PLAN("ai_plan", "AI规划", "smart_toy"),
//    MESSAGE("message", "消息", "message"),
    PROFILE("profile", "我的", "person")
}

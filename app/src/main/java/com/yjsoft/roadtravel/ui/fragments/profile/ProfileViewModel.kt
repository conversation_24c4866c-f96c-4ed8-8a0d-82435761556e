package com.yjsoft.roadtravel.ui.fragments.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatUserManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.WeChatUserInfoData
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 个人中心ViewModel
 * 
 * 功能：
 * - 管理微信用户信息
 * - 处理订单状态
 * - 会员信息管理
 * - 功能菜单处理
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
class ProfileViewModel : ViewModel() {
    
    companion object {
        private const val TAG = "ProfileViewModel"
    }
    
    private val weChatUserManager = WeChatUserManager.getInstance()
    
    // ========== 用户信息状态 ==========
    
    private val _userInfo = MutableStateFlow<WeChatUserInfoData?>(null)
    val userInfo: StateFlow<WeChatUserInfoData?> = _userInfo.asStateFlow()
    
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // ========== 会员信息状态 ==========
    
    private val _membershipInfo = MutableStateFlow(MembershipInfo())
    val membershipInfo: StateFlow<MembershipInfo> = _membershipInfo.asStateFlow()
    
    // ========== 订单状态 ==========
    
    private val _orderCounts = MutableStateFlow(OrderCounts())
    val orderCounts: StateFlow<OrderCounts> = _orderCounts.asStateFlow()
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "ProfileViewModel启动")
        loadUserInfo()
        loadMembershipInfo()
        loadOrderCounts()
    }
    
    // ========== 用户信息管理 ==========
    
    /**
     * 加载用户信息
     */
    fun loadUserInfo() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val loggedIn = weChatUserManager.isWeChatLoggedIn()
                _isLoggedIn.value = loggedIn
                
                if (loggedIn) {
                    val userInfo = weChatUserManager.getCurrentWeChatUser()
                    _userInfo.value = userInfo
                    LogManager.d(TAG, "用户信息加载成功: ${userInfo?.nickname}")
                } else {
                    _userInfo.value = null
                    LogManager.d(TAG, "用户未登录")
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "加载用户信息失败", e)
                _userInfo.value = null
                _isLoggedIn.value = false
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 刷新用户信息
     */
    fun refreshUserInfo() {
        loadUserInfo()
    }
    
    /**
     * 获取用户显示名称
     */
    suspend fun getUserDisplayName(): String {
        return try {
            weChatUserManager.getUserDisplayName()
        } catch (e: Exception) {
            LogManager.e(TAG, "获取用户显示名称失败", e)
            "微信用户"
        }
    }
    
    // ========== 会员信息管理 ==========
    
    /**
     * 加载会员信息
     */
    fun loadMembershipInfo() {
        viewModelScope.launch {
            try {
                // 模拟加载会员信息
                val membershipInfo = MembershipInfo(
                    type = "体验版",
                    expiryDate = "2024年8月31日",
                    points = 3234,
                    isVip = false
                )
                _membershipInfo.value = membershipInfo
                LogManager.d(TAG, "会员信息加载成功")
            } catch (e: Exception) {
                LogManager.e(TAG, "加载会员信息失败", e)
            }
        }
    }
    
    /**
     * 续费会员
     */
    fun renewMembership() {
        viewModelScope.launch {
            try {
                LogManager.d(TAG, "用户点击续费")
                // 这里可以跳转到续费页面
            } catch (e: Exception) {
                LogManager.e(TAG, "续费操作失败", e)
            }
        }
    }
    
    // ========== 订单管理 ==========
    
    /**
     * 加载订单统计
     */
    fun loadOrderCounts() {
        viewModelScope.launch {
            try {
                // 模拟加载订单统计数据
                val orderCounts = OrderCounts(
                    totalOrders = 12,
                    pendingPayment = 2,
                    pendingTravel = 1,
                    afterSales = 0
                )
                _orderCounts.value = orderCounts
                LogManager.d(TAG, "订单统计加载成功")
            } catch (e: Exception) {
                LogManager.e(TAG, "加载订单统计失败", e)
            }
        }
    }
    
    /**
     * 处理订单点击
     */
    fun handleOrderClick(orderType: String) {
        viewModelScope.launch {
            LogManager.d(TAG, "点击订单类型: $orderType")
            // 这里可以跳转到对应的订单页面
        }
    }
    
    // ========== 功能菜单处理 ==========
    
    /**
     * 处理功能菜单点击
     */
    fun handleMenuClick(menuTitle: String) {
        viewModelScope.launch {
            LogManager.d(TAG, "点击功能菜单: $menuTitle")
            
            when (menuTitle) {
                "行程收藏" -> {
                    // 跳转到行程收藏页面
                }
                "常用信息" -> {
                    // 跳转到常用信息页面
                }
                "机上WIFI" -> {
                    // 跳转到机上WIFI页面
                }
                "我的活动" -> {
                    // 跳转到我的活动页面
                }
                "邀请码" -> {
                    // 跳转到邀请码页面
                }
                "心愿单" -> {
                    // 跳转到心愿单页面
                }
                else -> {
                    LogManager.w(TAG, "未知的菜单项: $menuTitle")
                }
            }
        }
    }
    
    // ========== 账户操作 ==========
    
    /**
     * 退出登录
     */
    fun logout(onSuccess: (() -> Unit)? = null) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                LogManager.d(TAG, "开始执行退出登录")
                
                val success = weChatUserManager.weChatLogout()
                if (success) {
                    // 清除 ViewModel 中的状态
                    _userInfo.value = null
                    _isLoggedIn.value = false
                    
                    LogManager.d(TAG, "退出登录成功")
                    
                    // 执行成功回调
                    onSuccess?.invoke()
                } else {
                    LogManager.w(TAG, "退出登录失败")
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "退出登录异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // ========== 其他功能 ==========
    
    /**
     * 检查登录状态
     */
    fun checkLoginStatus(): Boolean {
        return _isLoggedIn.value
    }
    
    /**
     * 刷新所有数据
     */
    fun refresh() {
        loadUserInfo()
        loadMembershipInfo()
        loadOrderCounts()
    }
}

/**
 * 会员信息数据类
 */
data class MembershipInfo(
    val type: String = "体验版",
    val expiryDate: String = "2024年8月31日",
    val points: Int = 3234,
    val isVip: Boolean = false
)

/**
 * 订单统计数据类
 */
data class OrderCounts(
    val totalOrders: Int = 0,
    val pendingPayment: Int = 0,
    val pendingTravel: Int = 0,
    val afterSales: Int = 0
)

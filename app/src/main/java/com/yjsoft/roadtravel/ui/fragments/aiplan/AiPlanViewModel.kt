package com.yjsoft.roadtravel.ui.fragments.aiplan

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.launchWithUiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.logOperation
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * AI规划ViewModel
 * 
 * 功能：
 * - 管理AI规划列表
 * - 处理规划创建和删除
 * - 管理规划状态
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class AiPlanViewModel @Inject constructor() : BaseViewModel<AiPlanUiState>() {
    
    companion object {
        private const val TAG = "AiPlanViewModel %s"
    }
    
    // ========== 规划列表状态 ==========
    
    private val _planListState = MutableStateFlow<UiState<List<AiPlan>>>(UiState.Idle)
    val planListState: StateFlow<UiState<List<AiPlan>>> = _planListState.asStateFlow()
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): AiPlanUiState {
        return AiPlanUiState()
    }
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "AiPlanViewModel启动")
        loadPlans()
    }
    
    // ========== 规划管理 ==========
    
    /**
     * 加载规划列表
     */
    fun loadPlans() {
        launchWithUiState(_planListState, "加载AI规划中...") {
            // 模拟网络请求
            delay(1000)
            
            // 模拟数据
            generateMockPlans()
        }
    }
    
    /**
     * 创建新规划
     */
    fun createNewPlan() {
        safeLaunch {
            updateState { 
                it.copy(lastAction = "创建新规划")
            }
            
            showToast("开始创建AI规划...")
            
            // 模拟创建过程
            delay(1500)
            
            val newPlan = AiPlan(
                id = "plan_${System.currentTimeMillis()}",
                title = "新的AI规划",
                description = "AI智能生成的旅行规划",
                createdAt = getCurrentTimeString(),
                destinations = listOf("目的地1", "目的地2"),
                duration = "3天2夜"
            )
            
            // 更新列表
            val currentPlans = (_planListState.value as? UiState.Success)?.data ?: emptyList()
            val updatedPlans = listOf(newPlan) + currentPlans
            _planListState.value = UiState.Success(updatedPlans)
            
            updateState { 
                it.copy(
                    totalPlans = updatedPlans.size,
                    lastAction = "创建规划成功: ${newPlan.title}"
                )
            }
            
            showToast("AI规划创建成功！")
            LogManager.d(TAG, "创建新规划: ${newPlan.title}")
        }
    }
    
    /**
     * 打开规划详情
     */
    fun openPlan(planId: String) {
        safeLaunch {
            updateState { 
                it.copy(
                    selectedPlanId = planId,
                    lastAction = "打开规划: $planId"
                )
            }
            
            showToast("打开规划详情")
            LogManager.d(TAG, "打开规划: $planId")
            
            // 这里可以导航到规划详情页面
            navigateTo("plan_detail/$planId")
        }
    }
    
    /**
     * 删除规划
     */
    fun deletePlan(planId: String) {
        safeLaunch {
            val currentPlans = (_planListState.value as? UiState.Success)?.data ?: return@safeLaunch
            val planToDelete = currentPlans.find { it.id == planId }
            
            if (planToDelete != null) {
                val updatedPlans = currentPlans.filter { it.id != planId }
                _planListState.value = UiState.Success(updatedPlans)
                
                updateState { 
                    it.copy(
                        totalPlans = updatedPlans.size,
                        lastAction = "删除规划: ${planToDelete.title}"
                    )
                }
                
                showToast("规划已删除")
                LogManager.d(TAG, "删除规划: ${planToDelete.title}")
            }
        }
    }
    
    /**
     * 刷新规划列表
     */
    fun refresh() {
        loadPlans()
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 生成模拟规划数据
     */
    private fun generateMockPlans(): List<AiPlan> {
        return listOf(
            AiPlan(
                id = "plan_1",
                title = "北京3日游",
                description = "AI推荐的北京经典路线，包含故宫、长城、天坛等景点",
                createdAt = "2024-01-15",
                destinations = listOf("故宫", "长城", "天坛", "颐和园"),
                duration = "3天2夜"
            ),
            AiPlan(
                id = "plan_2",
                title = "上海周末游",
                description = "适合周末的上海精品路线，体验都市风情",
                createdAt = "2024-01-10",
                destinations = listOf("外滩", "东方明珠", "南京路", "豫园"),
                duration = "2天1夜"
            ),
            AiPlan(
                id = "plan_3",
                title = "杭州西湖游",
                description = "西湖美景一日游，感受江南水乡魅力",
                createdAt = "2024-01-05",
                destinations = listOf("西湖", "雷峰塔", "灵隐寺", "宋城"),
                duration = "1天"
            )
        )
    }
    
    /**
     * 获取当前时间字符串
     */
    private fun getCurrentTimeString(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
            .format(java.util.Date())
    }
    
    /**
     * 获取规划统计信息
     */
    fun getPlanStats(): String {
        val totalPlans = currentState.totalPlans
        return "共有 $totalPlans 个AI规划"
    }
}

/**
 * AI规划UI状态
 */
data class AiPlanUiState(
    val totalPlans: Int = 0,
    val selectedPlanId: String = "",
    val lastAction: String = ""
)

/**
 * AI规划数据类
 */
data class AiPlan(
    val id: String,
    val title: String,
    val description: String,
    val createdAt: String,
    val destinations: List<String>,
    val duration: String
)

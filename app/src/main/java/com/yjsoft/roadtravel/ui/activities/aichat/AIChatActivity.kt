package com.yjsoft.roadtravel.ui.activities.aichat

import android.os.Build
import android.os.Bundle
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.core.navigateTo
import com.yjsoft.roadtravel.ui.activities.login.LoginActivity
import com.yjsoft.roadtravel.ui.components.GradientBackground
import com.yjsoft.roadtravel.ui.components.IconSize
import com.yjsoft.roadtravel.ui.components.RightIconButton
import com.yjsoft.roadtravel.ui.components.TitleBar

class AIChatActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupContent {
            GradientBackground { AIChatScreen(onShowToast = { message -> showToast(message) }) }
        }
    }

    override fun finish() {
        super.finish()
        // 设置退出动画：当前页面向下滑出，上一个页面淡入
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            // Android 14+ 使用新的API
            overrideActivityTransition(
                    OVERRIDE_TRANSITION_CLOSE,
                    R.anim.fade_in,
                    R.anim.slide_down_out
            )
        } else {
            // 兼容旧版本
            @Suppress("DEPRECATION")
            overridePendingTransition(R.anim.fade_in, R.anim.slide_down_out)
        }
    }

    @Composable
    fun AIChatScreen(onShowToast: (String) -> Unit = {}) {
        val context = LocalContext.current
        // 配置状态栏样式 - 固定样式，不跟随系统色改变
        val view = LocalView.current
        SideEffect {
            val window = (view.context as AIChatActivity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)
            val insetsController = WindowCompat.getInsetsController(window, view)
            insetsController.isAppearanceLightStatusBars = true
        }
        Scaffold(modifier = Modifier.fillMaxSize(), containerColor = Color.Transparent) { padding ->
            Box(modifier = Modifier.padding(padding)) {
                TitleBar(
                        title = "行程规划",
                        includeStatusBarPadding = false,
                        titleIconColor = Color(0xFF1990FF),
                        backgroundColor = Color.Transparent,
                        leftIconSize = IconSize(24.dp),
                        titleLeftIconRes = R.drawable.chat_title_left,
                        leftIconRes = R.drawable.close,
                        titleLeftIconSize = IconSize(width = 30.dp, height = 15.dp),
                        titleRightIconSize = IconSize(24.dp),
                        onLeftIconClick = { onBackPressedDispatcher.onBackPressed() },
                        rightIcons =
                                listOf(
                                        RightIconButton(
                                                drawableRes = R.drawable.xinduihua,
                                                onClick = {
                                                    (context as AIChatActivity).navigateTo(
                                                            LoginActivity::class.java
                                                    )
                                                }
                                        ),
                                        RightIconButton(
                                                drawableRes = R.drawable.chat_history,
                                                onClick = {}
                                        ),
                                )
                )
            }
        }
    }
}

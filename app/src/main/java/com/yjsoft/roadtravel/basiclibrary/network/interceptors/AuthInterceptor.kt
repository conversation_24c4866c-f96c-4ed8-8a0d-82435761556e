package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

/**
 * 认证拦截器
 * 负责添加认证头、处理Token刷新和认证失败重定向
 */
class AuthInterceptor(
    private val context: Context,
    private val tokenProvider: TokenProvider
) : Interceptor {
    
    init {
        // 初始化TokenManager
        if (!TokenManager.isInitialized()) {
            TokenManager.initialize(context)
        }
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 如果是登录或刷新Token的请求，直接放行
        if (isAuthRequest(originalRequest)) {
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = addAuthHeader(originalRequest)
        val response = chain.proceed(authenticatedRequest)
        
        // 检查是否需要刷新Token
        if (response.code == 401 && shouldRefreshToken()) {
            response.close()
            
            // 尝试刷新Token
            val refreshed = runBlocking { refreshToken() }
            
            if (refreshed) {
                // Token刷新成功，重新发起请求
                val newRequest = addAuthHeader(originalRequest)
                return chain.proceed(newRequest)
            } else {
                // Token刷新失败，清除认证信息并重定向到登录页
                runBlocking { clearAuthInfo() }
                notifyAuthFailure()
            }
        }
        
        return response
    }
    
    /**
     * 添加认证头
     */
    private fun addAuthHeader(request: Request): Request {
        val token = getAccessToken()
        return if (token.isNotEmpty()) {
            request.newBuilder()
                .header(NetworkConfig.HEADER_AUTHORIZATION, "Bearer $token")
                .build()
        } else {
            request
        }
    }
    
    /**
     * 判断是否为认证相关请求
     */
    private fun isAuthRequest(request: Request): Boolean {
        val url = request.url.toString()
        return url.contains("/auth/login") || 
               url.contains("/auth/refresh") || 
               url.contains("/auth/register")
    }
    
    /**
     * 判断是否应该刷新Token
     */
    private fun shouldRefreshToken(): Boolean {
        return getRefreshToken().isNotEmpty()
    }
    
    /**
     * 刷新Token
     */
    private suspend fun refreshToken(): Boolean {
        return try {
            val refreshToken = getRefreshToken()
            if (refreshToken.isEmpty()) {
                false
            } else {
                val newTokens = tokenProvider.refreshToken(refreshToken)
                if (newTokens != null) {
                    saveTokens(newTokens.accessToken, newTokens.refreshToken)
                    true
                } else {
                    false
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 获取访问Token
     */
    private fun getAccessToken(): String {
        return TokenManager.getAccessToken()
    }
    
    /**
     * 获取刷新Token
     */
    private fun getRefreshToken(): String {
        return TokenManager.getRefreshToken()
    }
    
    /**
     * 保存Token
     */
    private suspend fun saveTokens(accessToken: String, refreshToken: String) {
        // 使用默认过期时间7200秒（2小时）
        TokenManager.updateTokens(accessToken, refreshToken, 7200)
    }
    
    /**
     * 清除认证信息
     */
    private suspend fun clearAuthInfo() {
        TokenManager.clearTokens()
    }
    
    /**
     * 通知认证失败
     */
    private fun notifyAuthFailure() {
        tokenProvider.onAuthFailure()
    }
    

}

/**
 * Token提供者接口
 */
interface TokenProvider {
    /**
     * 刷新Token
     * @param refreshToken 刷新Token
     * @return 新的Token信息，失败时返回null
     */
    suspend fun refreshToken(refreshToken: String): TokenInfo?
    
    /**
     * 认证失败回调
     */
    fun onAuthFailure()
}

/**
 * Token信息数据类
 */
data class TokenInfo(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long = 0L
)

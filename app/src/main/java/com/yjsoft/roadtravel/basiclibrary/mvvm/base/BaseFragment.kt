package com.yjsoft.roadtravel.basiclibrary.mvvm.base

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Event
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.NavigationEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiEvent
import com.yjsoft.roadtravel.ui.theme.RoadTravelTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

/**
 * Fragment基类
 * 
 * 功能：
 * - 集成Hilt依赖注入
 * - 统一的UI事件处理
 * - 统一的导航事件处理
 * - Compose集成
 * - 生命周期感知
 * 
 * 设计原则：
 * - 最小化样板代码
 * - 统一的事件处理机制
 * - 生命周期安全
 * - 主题统一
 * 
 * 使用方式：
 * ```kotlin
 * @AndroidEntryPoint
 * class UserFragment : BaseFragment() {
 * 
 *     @Inject
 *     lateinit var viewModel: UserViewModel
 * 
 *     override fun setupContent(): @Composable () -> Unit = {
 *         UserScreen(viewModel = viewModel)
 *     }
 * 
 *     override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
 *         super.onViewCreated(view, savedInstanceState)
 *         
 *         observeEvents(
 *             uiEvents = viewModel.uiEvents,
 *             navigationEvents = viewModel.navigationEvents
 *         )
 *     }
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@AndroidEntryPoint
abstract class BaseFragment : Fragment() {
    
    companion object {
        private const val TAG = "BaseFragment"
    }
    
    override fun onAttach(context: Context) {
        super.onAttach(context)
        LogManager.df("[%s] Fragment附加: %s", TAG, this::class.simpleName)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogManager.df("[%s] Fragment创建: %s", TAG, this::class.simpleName)
        
        // 初始化Fragment
        initializeFragment()
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        LogManager.df("[%s] Fragment创建视图: %s", TAG, this::class.simpleName)
        
        return ComposeView(requireContext()).apply {
            setContent {
                RoadTravelTheme {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = Color.Transparent // 让Surface背景透明
                    ) {
                        setupContent().invoke()
                    }
                }
            }
        }
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LogManager.df("[%s] Fragment视图创建完成: %s", TAG, this::class.simpleName)
    }
    
    override fun onStart() {
        super.onStart()
        LogManager.df("[%s] Fragment启动: %s", TAG, this::class.simpleName)
    }
    
    override fun onResume() {
        super.onResume()
        LogManager.df("[%s] Fragment恢复: %s", TAG, this::class.simpleName)
    }
    
    override fun onPause() {
        super.onPause()
        LogManager.df("[%s] Fragment暂停: %s", TAG, this::class.simpleName)
    }
    
    override fun onStop() {
        super.onStop()
        LogManager.df("[%s] Fragment停止: %s", TAG, this::class.simpleName)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        LogManager.df("[%s] Fragment销毁视图: %s", TAG, this::class.simpleName)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        LogManager.df("[%s] Fragment销毁: %s", TAG, this::class.simpleName)
    }
    
    override fun onDetach() {
        super.onDetach()
        LogManager.df("[%s] Fragment分离: %s", TAG, this::class.simpleName)
    }
    
    // ========== 抽象方法 ==========
    
    /**
     * 设置Compose内容
     * 子类必须实现此方法返回Compose内容
     */
    protected abstract fun setupContent(): @Composable () -> Unit
    
    // ========== 初始化方法 ==========
    
    /**
     * 初始化Fragment
     * 子类可以重写此方法进行自定义初始化
     */
    protected open fun initializeFragment() {
        // 默认实现为空，子类可以重写
    }
    
    // ========== 事件处理 ==========
    
    /**
     * 观察ViewModel事件
     */
    protected fun observeEvents(
        uiEvents: SharedFlow<Event<UiEvent>>? = null,
        navigationEvents: SharedFlow<Event<NavigationEvent>>? = null,
        customEvents: SharedFlow<Event<Any>>? = null
    ) {
        // 观察UI事件
        uiEvents?.let { events ->
            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { uiEvent ->
                            handleUiEvent(uiEvent)
                        }
                    }
                }
            }
        }
        
        // 观察导航事件
        navigationEvents?.let { events ->
            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { navigationEvent ->
                            handleNavigationEvent(navigationEvent)
                        }
                    }
                }
            }
        }
        
        // 观察自定义事件
        customEvents?.let { events ->
            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { customEvent ->
                            handleCustomEvent(customEvent)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 处理UI事件
     */
    open fun handleUiEvent(event: UiEvent) {
        when (event) {
            is UiEvent.ShowToast -> {
                showToast(event.message)
            }
            is UiEvent.ShowSnackbar -> {
                showSnackbar(event.message, event.actionLabel)
            }
            is UiEvent.ShowDialog -> {
                showDialog(event.title, event.message)
            }
            is UiEvent.HideKeyboard -> {
                hideKeyboard()
            }
            is UiEvent.ShowKeyboard -> {
                showKeyboard()
            }
        }
    }
    
    /**
     * 处理导航事件
     */
    open fun handleNavigationEvent(event: NavigationEvent) {
        when (event) {
            is NavigationEvent.Back -> {
                requireActivity().onBackPressedDispatcher.onBackPressed()
            }
            is NavigationEvent.ToRoute -> {
                navigateToRoute(event.route)
            }
            is NavigationEvent.ToActivity -> {
                navigateToActivity(event.className, event.params)
            }
        }
    }
    
    /**
     * 处理自定义事件
     * 子类可以重写此方法处理特定的自定义事件
     */
    open fun handleCustomEvent(event: Any) {
        LogManager.df("[%s] 收到自定义事件: %s", TAG, event::class.simpleName)
    }
    
    // ========== UI操作方法 ==========
    
    /**
     * 显示Toast
     */
    protected open fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示Snackbar
     */
    protected open fun showSnackbar(message: String, actionLabel: String? = null) {
        // 默认实现使用Toast，子类可以重写使用真正的Snackbar
        showToast(message)
    }
    
    /**
     * 显示对话框
     */
    protected open fun showDialog(title: String, message: String) {
        // 默认实现使用Toast，子类可以重写使用真正的Dialog
        showToast("$title: $message")
    }
    
    /**
     * 隐藏键盘
     */
    protected open fun hideKeyboard() {
        // 子类可以实现具体的隐藏键盘逻辑
    }
    
    /**
     * 显示键盘
     */
    protected open fun showKeyboard() {
        // 子类可以实现具体的显示键盘逻辑
    }
    
    // ========== 导航方法 ==========
    
    /**
     * 导航到指定路由
     * 子类需要实现具体的导航逻辑
     */
    protected open fun navigateToRoute(route: String) {
        LogManager.df("[%s] 导航到路由: %s", TAG, route)
        // 子类实现具体的导航逻辑
    }
    
    /**
     * 导航到指定Activity
     */
    protected open fun navigateToActivity(className: String, params: Map<String, Any> = emptyMap()) {
        LogManager.df("[%s] 导航到Activity: %s, 参数: %s", TAG, className, params)
        // 子类实现具体的Activity跳转逻辑
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取Fragment名称
     */
    protected fun getFragmentName(): String {
        return this::class.simpleName ?: "UnknownFragment"
    }
    
    /**
     * 检查Fragment是否处于活跃状态
     */
    protected fun isFragmentActive(): Boolean {
        return isAdded && !isDetached && !isRemoving
    }
    
    /**
     * 安全地获取Context
     */
    protected fun safeContext(): Context? {
        return if (isFragmentActive()) requireContext() else null
    }
}

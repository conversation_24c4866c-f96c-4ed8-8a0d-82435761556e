# 微信登录功能使用说明

## 概述

本模块实现了完整的微信登录功能，基于微信开放平台的OAuth2.0授权流程。

## 功能特性

- ✅ 完整的OAuth2.0授权流程
- ✅ 自动检测微信安装状态
- ✅ 完善的错误处理机制
- ✅ MVVM架构集成
- ✅ Hilt依赖注入支持
- ✅ 详细的日志记录

## 配置说明

### 1. 微信开放平台配置

在 `WeChatConfig.kt` 中配置您的微信应用参数：

```kotlin
object WeChatConfig {
    const val APP_ID = "您的微信AppId"
    const val APP_SECRET = "您的微信AppSecret"
}
```

### 2. AndroidManifest.xml 配置

确保已正确注册微信回调Activity：

```xml
<activity
    android:name=".wxapi.WXEntryActivity"
    android:exported="true"
    android:launchMode="singleTop"
    android:theme="@android:style/Theme.Translucent.NoTitleBar" />
```

### 3. 包名结构要求

微信回调Activity必须放在 `包名.wxapi` 包下，类名必须为 `WXEntryActivity`。

## 使用方法

### 在ViewModel中使用

```kotlin
@HiltViewModel
class YourViewModel @Inject constructor(
    private val weChatLoginService: WeChatLoginService
) : ViewModel() {
    
    fun loginWithWeChat() {
        viewModelScope.launch {
            val result = weChatLoginService.performLogin()
            when (result) {
                is WeChatLoginServiceResult.Success -> {
                    // 登录成功，处理用户信息
                    val userInfo = result.userInfo
                }
                is WeChatLoginServiceResult.Error -> {
                    // 登录失败
                }
                // 其他情况...
            }
        }
    }
}
```

### 在Compose UI中使用

```kotlin
@Composable
fun LoginScreen(viewModel: LoginViewModel) {
    val loginState by viewModel.loginState.collectAsState()
    
    Button(
        onClick = { viewModel.loginWithWeChat() },
        enabled = loginState !is UiState.Loading
    ) {
        Text("微信登录")
    }
}
```

## 授权流程

1. **发起授权** - 调用微信SDK发起授权请求
2. **用户授权** - 用户在微信中确认授权
3. **获取Code** - 微信返回授权码
4. **换取Token** - 使用授权码获取access_token
5. **获取用户信息** - 使用access_token获取用户基本信息
6. **完成登录** - 返回用户信息和登录状态

## 错误处理

系统会自动处理以下错误情况：

- 微信未安装
- 用户取消授权
- 用户拒绝授权
- 网络请求失败
- Token获取失败
- 用户信息获取失败

## 注意事项

1. **AppSecret安全性**: 在生产环境中，AppSecret应该存储在服务器端，不应暴露在客户端代码中
2. **微信安装检测**: iOS应用建议先检测微信是否已安装，未安装时隐藏微信登录按钮
3. **包名配置**: 确保在微信开放平台配置的包名与应用包名一致
4. **签名配置**: 发布版本需要在微信开放平台配置正确的应用签名

## 依赖项

- 微信OpenSDK
- Retrofit (网络请求)
- Hilt (依赖注入)
- Kotlin协程

## 相关文件

- `WeChatConfig.kt` - 配置管理
- `WeChatLoginManager.kt` - 登录管理器
- `WeChatLoginService.kt` - 登录服务
- `WeChatApiService.kt` - API接口定义
- `WXEntryActivity.kt` - 微信回调Activity
- `LoginViewModel.kt` - 登录页面ViewModel
- `LoginActivity.kt` - 登录页面Activity

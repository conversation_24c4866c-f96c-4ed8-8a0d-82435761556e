package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.runBlocking
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

/**
 * 公共参数拦截器
 * 为所有请求自动添加公共参数：zone_id, lat, lng, channel
 */
class CommonParamsInterceptor(
    private val context: Context
) : Interceptor {
    
    companion object {
        private const val TAG = "CommonParamsInterceptor"
        
        // 公共参数名称
        private const val PARAM_ZONE_ID = "zone_id"
        private const val PARAM_LAT = "lat"
        private const val PARAM_LNG = "lng"
        private const val PARAM_CHANNEL = "channel"
        
        // 默认值
        private const val DEFAULT_CHANNEL = "android"
        private const val DEFAULT_ZONE_ID = 1
        private const val DEFAULT_LAT = 0.0
        private const val DEFAULT_LNG = 0.0
    }
    
    // DataStore仓库，用于获取用户设置
    private val dataStoreRepository: DataStoreRepository by lazy {
        DataStoreRepository.getInstance()
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 如果是登录相关请求，可能不需要某些公共参数
        if (isAuthRequest(originalRequest)) {
            // 登录请求只添加channel参数
            val newRequest = addChannelParam(originalRequest)
            return chain.proceed(newRequest)
        }
        
        // 为普通请求添加所有公共参数
        val newRequest = addCommonParams(originalRequest)
        return chain.proceed(newRequest)
    }
    
    /**
     * 添加所有公共参数
     */
    private fun addCommonParams(request: Request): Request {
        val originalUrl = request.url
        val urlBuilder = originalUrl.newBuilder()
        
        // 获取公共参数值
        val commonParams = getCommonParams()
        
        // 添加参数（如果URL中已存在则不覆盖）
        commonParams.forEach { (key, value) ->
            if (originalUrl.queryParameter(key) == null) {
                urlBuilder.addQueryParameter(key, value)
            }
        }
        
        val newUrl = urlBuilder.build()
        
        LogManager.d(TAG, "添加公共参数: ${request.method} $newUrl")
        
        return request.newBuilder()
            .url(newUrl)
            .build()
    }
    
    /**
     * 只添加channel参数
     */
    private fun addChannelParam(request: Request): Request {
        val originalUrl = request.url
        
        // 如果已经有channel参数，则不添加
        if (originalUrl.queryParameter(PARAM_CHANNEL) != null) {
            return request
        }
        
        val newUrl = originalUrl.newBuilder()
            .addQueryParameter(PARAM_CHANNEL, DEFAULT_CHANNEL)
            .build()
        
        LogManager.d(TAG, "添加channel参数: ${request.method} $newUrl")
        
        return request.newBuilder()
            .url(newUrl)
            .build()
    }
    
    /**
     * 获取公共参数
     */
    private fun getCommonParams(): Map<String, String> {
        return try {
            // 使用runBlocking获取DataStore中的数据
            runBlocking {
                val zoneId = getZoneId()
                val location = getLocation()
                
                mapOf(
                    PARAM_ZONE_ID to zoneId.toString(),
                    PARAM_LAT to location.first.toString(),
                    PARAM_LNG to location.second.toString(),
                    PARAM_CHANNEL to DEFAULT_CHANNEL
                )
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取公共参数失败，使用默认值", e)
            // 出错时使用默认值
            mapOf(
                PARAM_ZONE_ID to DEFAULT_ZONE_ID.toString(),
                PARAM_LAT to DEFAULT_LAT.toString(),
                PARAM_LNG to DEFAULT_LNG.toString(),
                PARAM_CHANNEL to DEFAULT_CHANNEL
            )
        }
    }
    
    /**
     * 获取区域ID
     */
    private suspend fun getZoneId(): Int {
        return try {
            // 从DataStore获取用户设置的区域ID
            // 这里假设有相关的preference key，如果没有则使用默认值
            DEFAULT_ZONE_ID
        } catch (e: Exception) {
            LogManager.w(TAG, "获取zone_id失败，使用默认值", e)
            DEFAULT_ZONE_ID
        }
    }
    
    /**
     * 获取位置信息
     * @return Pair<lat, lng>
     */
    private suspend fun getLocation(): Pair<Double, Double> {
        return try {
            // 从DataStore获取用户的位置信息
            // 这里假设有相关的preference key，如果没有则使用默认值
            Pair(DEFAULT_LAT, DEFAULT_LNG)
        } catch (e: Exception) {
            LogManager.w(TAG, "获取位置信息失败，使用默认值", e)
            Pair(DEFAULT_LAT, DEFAULT_LNG)
        }
    }
    
    /**
     * 判断是否为认证相关请求
     */
    private fun isAuthRequest(request: Request): Boolean {
        val url = request.url.toString()
        return url.contains("/auth/login") || 
               url.contains("/auth/refresh") || 
               url.contains("/auth/register") ||
               url.contains("/front/login")
    }
}

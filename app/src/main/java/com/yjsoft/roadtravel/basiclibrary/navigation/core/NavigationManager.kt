package com.yjsoft.roadtravel.basiclibrary.navigation.core

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.core.app.ActivityOptionsCompat
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.models.NavigationParams
import com.yjsoft.roadtravel.basiclibrary.navigation.utils.ParamSerializer
import com.yjsoft.roadtravel.basiclibrary.navigation.WebViewActivity
import com.yjsoft.roadtravel.basiclibrary.payment.PaymentActivity

/**
 * 多Activity架构的导航管理器
 * 负责Activity间的跳转、参数传递和结果处理
 *
 * 使用示例：
 * ```kotlin
 * // 基本跳转
 * activity.navigateTo(NavigationConstants.ActivityClasses.AI_CHAT)
 *
 * // 带参数跳转
 * val params = NavigationParams.builder()
 *     .putString("title", "AI聊天")
 *     .putInt("userId", 123)
 *     .build()
 * activity.navigateTo(NavigationConstants.ActivityClasses.AI_CHAT, params)
 *
 * // 登录成功后跳转主页面
 * activity.navigateToMainAndClearTask(NavigationConstants.ActivityClasses.MAIN)
 * ```
 *
 * 注意：为了避免内存泄漏，此类不再持有Context引用
 * 所有方法都需要传入Context参数
 */
object NavigationManager {

    private const val TAG = "NavigationManager %s"

    /**
     * 跳转到支付演示页面
     */
    fun navigateToPaymentDemo(
        activity: BaseActivity,
        params: NavigationParams = NavigationParams.empty(),
        launcher: ActivityResultLauncher<Intent>? = null
    ) {
        try {
            // 使用显式Intent，直接指定PaymentActivity类
            val intent = Intent(activity, PaymentActivity::class.java).apply {
                putExtra(NavigationConstants.Params.FROM, activity.javaClass.simpleName)
                
                // 添加参数
                params.stringParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.intParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.booleanParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.longParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.doubleParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
            }
            
            if (launcher != null) {
                launcher.launch(intent)
            } else {
                activity.startActivityForResult(intent, NavigationConstants.RequestCodes.PAYMENT_REQUEST)
            }
            
            LogManager.d(TAG, "导航到支付演示页面")
        } catch (e: Exception) {
            LogManager.e(TAG, "支付演示页面跳转失败", e)
        }
    }

    /**
     * 导航到WebView页面
     */
    fun navigateToWebView(
        activity: BaseActivity,
        url: String,
        title: String? = null,
        params: NavigationParams = NavigationParams.empty(),
        launcher: ActivityResultLauncher<Intent>? = null
    ) {
        try {
            val intent = Intent(activity, WebViewActivity::class.java).apply {
                putExtra(NavigationConstants.Params.URL, url)
                putExtra(NavigationConstants.Params.TITLE, title ?: NavigationConstants.WebView.DEFAULT_TITLE)
                putExtra(NavigationConstants.Params.FROM, activity.javaClass.simpleName)
                
                // 添加自定义参数
                ParamSerializer.addParamsToIntent(this, params)
            }
            
            if (launcher != null) {
                launcher.launch(intent)
            } else {
                activity.startActivityForResult(intent, NavigationConstants.RequestCodes.WEBVIEW_REQUEST)
            }
            
            LogManager.d(TAG, "导航到WebView页面: $url")
        } catch (e: Exception) {
            LogManager.e(TAG, "WebView页面跳转失败", e)
        }
    }

    /**
     * 跳转到指定Activity
     */
    fun navigateToActivity(
        activity: BaseActivity,
        targetClass: Class<*>,
        params: NavigationParams = NavigationParams.empty(),
        requestCode: Int = 0,
        launcher: ActivityResultLauncher<Intent>? = null,
        options: ActivityOptionsCompat? = null,
        intentFlags: Int? = null
    ) {
        try {
            val intent = Intent(activity, targetClass).apply {
                putExtra(NavigationConstants.Params.FROM, activity.javaClass.simpleName)

                // 设置Intent标志位
                intentFlags?.let { flags = it }

                // 添加参数
                params.stringParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.intParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.booleanParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.longParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.doubleParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
            }

            when {
                launcher != null -> launcher.launch(intent)
                requestCode > 0 -> activity.startActivityForResult(intent, requestCode)
                else -> {
                    if (options != null) {
                        activity.startActivity(intent, options.toBundle())
                    } else {
                        activity.startActivity(intent)
                    }
                }
            }

            LogManager.d(TAG, "导航到Activity: ${targetClass.simpleName}")
        } catch (e: Exception) {
            LogManager.e(TAG, "Activity跳转失败: ${targetClass.simpleName}", e)
        }
    }

    /**
     * 跳转到指定Activity（通用版本，支持任何Activity类型）
     */
    fun navigateToActivity(
        activity: Activity,
        targetClass: Class<*>,
        params: NavigationParams = NavigationParams.empty(),
        requestCode: Int = 0,
        launcher: ActivityResultLauncher<Intent>? = null,
        options: ActivityOptionsCompat? = null,
        intentFlags: Int? = null
    ) {
        try {
            val intent = Intent(activity, targetClass).apply {
                putExtra(NavigationConstants.Params.FROM, activity.javaClass.simpleName)

                // 设置Intent标志位
                intentFlags?.let { flags = it }

                // 添加参数
                ParamSerializer.addParamsToIntent(this, params)
            }

            when {
                launcher != null -> launcher.launch(intent)
                requestCode > 0 -> activity.startActivityForResult(intent, requestCode)
                else -> {
                    if (options != null) {
                        activity.startActivity(intent, options.toBundle())
                    } else {
                        activity.startActivity(intent)
                    }
                }
            }

            LogManager.d(TAG, "导航到Activity: ${targetClass.simpleName}")
        } catch (e: Exception) {
            LogManager.e(TAG, "Activity跳转失败: ${targetClass.simpleName}", e)
        }
    }

    /**
     * 跳转到AI聊天页面，带从下向上的进入动画
     */
    fun navigateToAIChat(
        activity: Activity,
        params: NavigationParams = NavigationParams.empty()
    ) {
        // 使用自定义的从下向上进入动画
        val slideUpOptions = ActivityOptionsCompat.makeCustomAnimation(
            activity,
            com.yjsoft.roadtravel.R.anim.slide_up_in,  // 从下向上进入
            com.yjsoft.roadtravel.R.anim.fade_out      // 当前页面淡出
        )

        navigateToActivity(
            activity = activity,
            targetClass = com.yjsoft.roadtravel.ui.activities.aichat.AIChatActivity::class.java,
            params = params,
            options = slideUpOptions
        )
    }

    /**
     * 跳转到主页面并清除任务栈
     * 专门用于登录成功后的跳转
     */
    fun navigateToMainAndClearTask(
        activity: Activity,
        targetClass: Class<*>,
        params: NavigationParams = NavigationParams.empty()
    ) {
        navigateToActivity(
            activity = activity,
            targetClass = targetClass,
            params = params,
            intentFlags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        )
    }

    /**
     * 处理深度链接
     */
    fun handleDeepLink(activity: Activity, deepLink: String): Boolean {
        return try {
            // TODO: 实现深度链接解析和跳转逻辑
            LogManager.d(TAG, "处理深度链接: $deepLink")
            false
        } catch (e: Exception) {
            LogManager.e(TAG, "深度链接处理失败: $deepLink", e)
            false
        }
    }

    /**
     * 关闭当前Activity并返回结果
     */
    fun finishWithResult(
        activity: BaseActivity,
        resultCode: Int,
        params: NavigationParams = NavigationParams.empty()
    ) {
        try {
            val resultIntent = Intent().apply {
                params.stringParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.intParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
                params.booleanParams.forEach { (key, value) ->
                    putExtra(key, value)
                }
            }

            activity.setResult(resultCode, resultIntent)
            activity.finish()

            LogManager.d(TAG, "Activity结束并返回结果: $resultCode")
        } catch (e: Exception) {
            LogManager.e(TAG, "Activity结束失败", e)
        }
    }
}

/**
 * Activity扩展函数，简化导航调用
 */
fun Activity.navigateTo(
    targetClass: Class<*>,
    params: NavigationParams = NavigationParams.empty(),
    requestCode: Int = 0,
    launcher: ActivityResultLauncher<Intent>? = null,
    options: ActivityOptionsCompat? = null,
    intentFlags: Int? = null
) {
    NavigationManager.navigateToActivity(
        activity = this,
        targetClass = targetClass,
        params = params,
        requestCode = requestCode,
        launcher = launcher,
        options = options,
        intentFlags = intentFlags
    )
}

/**
 * Activity扩展函数，跳转到AI聊天页面，带从下向上的进入动画
 */
fun Activity.navigateToAIChat(
    params: NavigationParams = NavigationParams.empty()
) {
    NavigationManager.navigateToAIChat(
        activity = this,
        params = params
    )
}

/**
 * Activity扩展函数，跳转到主页面并清除任务栈
 */
fun Activity.navigateToMainAndClearTask(
    targetClass: Class<*>,
    params: NavigationParams = NavigationParams.empty()
) {
    NavigationManager.navigateToMainAndClearTask(
        activity = this,
        targetClass = targetClass,
        params = params
    )
}
package com.yjsoft.roadtravel.basiclibrary.mvvm.base

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import retrofit2.HttpException
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Repository基类
 * 
 * 功能：
 * - 统一的数据访问模式
 * - 统一的异常处理
 * - 网络和本地数据源管理
 * - 缓存策略支持
 * - 线程切换管理
 * 
 * 设计原则：
 * - 单一数据源：Repository是UI层访问数据的唯一入口
 * - 异常安全：统一的异常处理和转换
 * - 线程安全：自动处理线程切换
 * - 缓存优先：优先使用本地缓存
 * 
 * 使用方式：
 * ```kotlin
 * @Singleton
 * class UserRepository @Inject constructor(
 *     private val apiService: ApiService,
 *     private val userDao: UserDao
 * ) : BaseRepository() {
 * 
 *     suspend fun getUser(id: String): Resource<User> = safeApiCall {
 *         apiService.getUser(id)
 *     }
 * 
 *     fun getUserFlow(id: String): Flow<Resource<User>> = networkBoundResource(
 *         query = { userDao.getUserById(id) },
 *         fetch = { apiService.getUser(id) },
 *         saveFetchResult = { user -> userDao.insertUser(user) }
 *     )
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
abstract class BaseRepository {
    
    companion object {
        private const val TAG = "BaseRepository"
    }
    
    // ========== 网络请求封装 ==========
    
    /**
     * 安全的API调用
     * 自动处理异常并转换为Resource
     */
    protected suspend fun <T> safeApiCall(
        apiCall: suspend () -> T
    ): Resource<T> {
        return withContext(Dispatchers.IO) {
            try {
                LogManager.d("[%s] 开始API调用", TAG)
                val result = apiCall()
                LogManager.d("[%s] API调用成功", TAG)
                Resource.Success(result)
            } catch (exception: Exception) {
                LogManager.e(exception, "[%s] API调用失败", TAG)
                handleApiException(exception)
            }
        }
    }
    
    /**
     * 安全的API调用（带加载状态）
     */
    protected fun <T> safeApiCallFlow(
        apiCall: suspend () -> T
    ): Flow<Resource<T>> = flow {
        emit(Resource.Loading())
        try {
            LogManager.d("[%s] 开始API调用流", TAG)
            val result = apiCall()
            LogManager.d("[%s] API调用流成功", TAG)
            emit(Resource.Success(result))
        } catch (exception: Exception) {
            LogManager.e(exception, "[%s] API调用流失败", TAG)
            emit(handleApiException(exception))
        }
    }.flowOn(Dispatchers.IO)
    
    // ========== 网络绑定资源 ==========
    
    /**
     * 网络绑定资源
     * 实现缓存优先的数据加载策略
     */
    protected fun <T, A> networkBoundResource(
        query: suspend () -> T?,
        fetch: suspend () -> A,
        saveFetchResult: suspend (A) -> Unit,
        shouldFetch: (T?) -> Boolean = { true },
        onFetchFailed: (Throwable) -> Unit = {}
    ): Flow<Resource<T>> = flow {
        // 首先发射加载状态
        emit(Resource.Loading())
        
        // 查询本地数据
        val localData = query()
        
        // 如果有本地数据，先发射本地数据
        if (localData != null) {
            emit(Resource.Loading(localData))
        }
        
        // 判断是否需要从网络获取
        if (shouldFetch(localData)) {
            try {
                LogManager.d("[%s] 从网络获取数据", TAG)
                val networkData = fetch()
                saveFetchResult(networkData)
                
                // 重新查询本地数据并发射
                val newLocalData = query()
                if (newLocalData != null) {
                    emit(Resource.Success(newLocalData))
                } else {
                    emit(Resource.Error(message = "保存数据后查询失败"))
                }
            } catch (exception: Exception) {
                LogManager.e(exception, "[%s] 网络获取数据失败", TAG)
                onFetchFailed(exception)

                // 如果有本地数据，发射本地数据；否则发射错误
                if (localData != null) {
                    emit(Resource.Success(localData))
                } else {
                    emit(handleApiException(exception))
                }
            }
        } else {
            // 不需要从网络获取，直接使用本地数据
            if (localData != null) {
                emit(Resource.Success(localData))
            } else {
                emit(Resource.Error(message = "无本地数据"))
            }
        }
    }.catch { exception ->
        LogManager.e(exception, "[%s] 网络绑定资源异常", TAG)
        val apiException = exception as? Exception ?: Exception(exception)
        emit(handleApiException(apiException))
    }.flowOn(Dispatchers.IO)
    
    // ========== 异常处理 ==========
    
    /**
     * 处理API异常
     */
    private fun <T> handleApiException(exception: Exception): Resource<T> {
        return when (exception) {
            is HttpException -> {
                val code = exception.code()
                val message = when (code) {
                    400 -> "请求参数错误"
                    401 -> "未授权访问"
                    403 -> "禁止访问"
                    404 -> "请求的资源不存在"
                    408 -> "请求超时"
                    500 -> "服务器内部错误"
                    502 -> "网关错误"
                    503 -> "服务不可用"
                    else -> "网络请求失败 (${code})"
                }
                Resource.Error(exception, message, code)
            }
            is UnknownHostException -> {
                Resource.Error(exception, "网络连接失败，请检查网络设置")
            }
            is ConnectException -> {
                Resource.Error(exception, "无法连接到服务器")
            }
            is SocketTimeoutException -> {
                Resource.Error(exception, "网络请求超时")
            }
            is IOException -> {
                Resource.Error(exception, "网络连接异常")
            }
            else -> {
                Resource.Error(exception, exception.message ?: "未知错误")
            }
        }
    }
    
    // ========== 缓存策略 ==========
    
    /**
     * 缓存策略枚举
     */
    enum class CacheStrategy {
        /** 仅使用缓存 */
        CACHE_ONLY,
        /** 仅使用网络 */
        NETWORK_ONLY,
        /** 缓存优先，网络备用 */
        CACHE_FIRST,
        /** 网络优先，缓存备用 */
        NETWORK_FIRST,
        /** 先返回缓存，再更新网络数据 */
        CACHE_THEN_NETWORK
    }
    
    /**
     * 根据缓存策略获取数据
     */
    protected fun <T> getDataWithStrategy(
        strategy: CacheStrategy,
        cacheCall: suspend () -> T?,
        networkCall: suspend () -> T,
        saveToCache: suspend (T) -> Unit = {}
    ): Flow<Resource<T>> = flow {
        when (strategy) {
            CacheStrategy.CACHE_ONLY -> {
                val cacheData = cacheCall()
                if (cacheData != null) {
                    emit(Resource.Success(cacheData))
                } else {
                    emit(Resource.Error(message = "无缓存数据"))
                }
            }
            
            CacheStrategy.NETWORK_ONLY -> {
                emit(Resource.Loading())
                try {
                    val networkData = networkCall()
                    saveToCache(networkData)
                    emit(Resource.Success(networkData))
                } catch (e: Exception) {
                    emit(handleApiException(e))
                }
            }
            
            CacheStrategy.CACHE_FIRST -> {
                val cacheData = cacheCall()
                if (cacheData != null) {
                    emit(Resource.Success(cacheData))
                } else {
                    emit(Resource.Loading())
                    try {
                        val networkData = networkCall()
                        saveToCache(networkData)
                        emit(Resource.Success(networkData))
                    } catch (e: Exception) {
                        emit(handleApiException(e))
                    }
                }
            }
            
            CacheStrategy.NETWORK_FIRST -> {
                emit(Resource.Loading())
                try {
                    val networkData = networkCall()
                    saveToCache(networkData)
                    emit(Resource.Success(networkData))
                } catch (e: Exception) {
                    val cacheData = cacheCall()
                    if (cacheData != null) {
                        emit(Resource.Success(cacheData))
                    } else {
                        emit(handleApiException(e))
                    }
                }
            }
            
            CacheStrategy.CACHE_THEN_NETWORK -> {
                val cacheData = cacheCall()
                if (cacheData != null) {
                    emit(Resource.Loading(cacheData))
                } else {
                    emit(Resource.Loading())
                }
                
                try {
                    val networkData = networkCall()
                    saveToCache(networkData)
                    emit(Resource.Success(networkData))
                } catch (e: Exception) {
                    if (cacheData != null) {
                        emit(Resource.Success(cacheData))
                    } else {
                        emit(handleApiException(e))
                    }
                }
            }
        }
    }.catch { exception ->
        val apiException = exception as? Exception ?: Exception(exception)
        emit(handleApiException(apiException))
    }.flowOn(Dispatchers.IO)
    
    // ========== 工具方法 ==========
    
    /**
     * 检查网络连接
     */
    protected fun isNetworkAvailable(): Boolean {
        return try {
            val status = NetworkManager.getStatus()
            status.currentEnvironment != null
        } catch (e: Exception) {
            LogManager.w("[%s] 检查网络状态失败: %s", TAG, e.message)
            false
        }
    }
    
    /**
     * 记录Repository操作
     */
    protected fun logOperation(operation: String, details: String = "") {
        LogManager.d("[%s] %s %s", this::class.simpleName, operation, details)
    }
}

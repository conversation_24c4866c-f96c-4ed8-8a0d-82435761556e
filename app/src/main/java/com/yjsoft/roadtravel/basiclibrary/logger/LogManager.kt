package com.yjsoft.roadtravel.basiclibrary.logger

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.trees.CrashReportingTree
import com.yjsoft.roadtravel.basiclibrary.logger.trees.DebugTree
import com.yjsoft.roadtravel.basiclibrary.logger.trees.FileTree
import timber.log.Timber

/**
 * 日志管理器
 * 统一管理应用的日志输出，基于Timber框架
 */
object LogManager {

    private var isInitialized = false
    private var currentLogLevel = LogConfig.LogLevel.DEBUG

    /**
     * 初始化日志框架
     * @param context 应用上下文
     * @param isDebug 是否为调试模式
     */
    fun init(context: Context, isDebug: Boolean = isDebugBuild(context)) {
        if (isInitialized) {
            Timber.w("LogManager已经初始化，跳过重复初始化")
            return
        }

        // 清除所有现有的Tree
        Timber.uprootAll()

        // 设置当前日志级别
        currentLogLevel = LogConfig.getDefaultLogLevel(isDebug)

        if (isDebug) {
            // 调试模式：添加调试Tree
            Timber.plant(DebugTree())
            Timber.d("调试模式日志已启用")
        }

        // 添加文件日志Tree（生产和调试环境都启用）
        if (LogConfig.FileLog.isEnabled) {
            try {
                val fileTree = FileTree(context)
                Timber.plant(fileTree)
                Timber.d("文件日志已启用")
            } catch (e: Exception) {
                Timber.e(e, "文件日志初始化失败")
            }
        }

        // 添加崩溃日志Tree
        if (LogConfig.CrashLog.isEnabled) {
            try {
                val crashTree = CrashReportingTree(context)
                Timber.plant(crashTree)
                Timber.d("崩溃日志收集已启用")
            } catch (e: Exception) {
                Timber.e(e, "崩溃日志初始化失败")
            }
        }

        isInitialized = true
        Timber.i("LogManager初始化完成，当前日志级别: ${currentLogLevel.name}")
    }

    /**
     * 设置日志级别
     */
    fun setLogLevel(level: LogConfig.LogLevel) {
        currentLogLevel = level
        Timber.i("日志级别已更新为: ${level.name}")
    }

    /**
     * 获取当前日志级别
     */
    fun getCurrentLogLevel(): LogConfig.LogLevel = currentLogLevel

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    // ========== 日志显示配置方法 ==========

    /**
     * 设置为仅调用者信息模式（推荐）
     * 标签显示简单类名，消息中显示详细调用者信息
     */
    fun setCallerInfoOnlyMode() {
        LogConfig.Debug.logDisplayMode = LogConfig.LogDisplayMode.CALLER_INFO_ONLY
        Timber.i("日志显示模式已设置为: 仅调用者信息模式")
    }

    /**
     * 设置为最简模式
     * 不显示额外的调用者信息，只显示原始消息
     */
    fun setMinimalMode() {
        LogConfig.Debug.logDisplayMode = LogConfig.LogDisplayMode.MINIMAL
        Timber.i("日志显示模式已设置为: 最简模式")
    }

    /**
     * 设置为标准模式
     * 在标签或消息中显示完整的调用者信息
     */
    fun setStandardMode(showDetailedTag: Boolean = false) {
        LogConfig.Debug.logDisplayMode = LogConfig.LogDisplayMode.STANDARD
        LogConfig.Debug.showDetailedTag = showDetailedTag
        Timber.i("日志显示模式已设置为: 标准模式 (详细标签: $showDetailedTag)")
    }

    /**
     * 配置调用者信息显示选项
     */
    fun configureCallerInfo(
        showThreadInfo: Boolean = true,
        showMethodInfo: Boolean = true,
        showFileInfo: Boolean = true
    ) {
        LogConfig.Debug.showThreadInfo = showThreadInfo
        LogConfig.Debug.showMethodInfo = showMethodInfo
        LogConfig.Debug.showFileInfo = showFileInfo
        Timber.i("调用者信息配置已更新: 线程=$showThreadInfo, 方法=$showMethodInfo, 文件=$showFileInfo")
    }

    // ========== 便捷日志方法 ==========

    /**
     * 输出Verbose级别日志
     */
    fun v(message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.VERBOSE)) {
            Timber.v(message, *args)
        }
    }

    /**
     * 输出Verbose级别日志（带异常）
     */
    fun v(t: Throwable?, message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.VERBOSE)) {
            Timber.v(t, message, *args)
        }
    }

    /**
     * 输出Debug级别日志
     */
    fun d(message: String) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            Timber.d(message)
        }
    }

    /**
     * 输出Debug级别日志
     */
    fun d(message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            if (!message.contains("%s")) {
                Timber.d("%s %s", message, *args)
            }
            Timber.d(message, *args)
        }
    }

    /**
     * 输出Debug级别日志（带异常）
     */
    fun d(t: Throwable?, message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            Timber.d(t, message, *args)
        }
    }

    /**
     * 输出Info级别日志
     */
    fun i(message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            Timber.i(message, *args)
        }
    }

    /**
     * 输出Info级别日志（带异常）
     */
    fun i(t: Throwable?, message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            Timber.i(t, message, *args)
        }
    }

    /**
     * 输出Warning级别日志
     */
    fun w(message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.WARN)) {
            Timber.w(message, *args)
        }
    }

    /**
     * 输出Warning级别日志（带异常）
     */
    fun w(t: Throwable?, message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.WARN)) {
            Timber.w(t, message, *args)
        }
    }

    /**
     * 输出Error级别日志
     */
    fun e(message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.ERROR)) {
            Timber.e(message, *args)
        }
    }

    /**
     * 输出Error级别日志（带异常）
     */
    fun e(t: Throwable?, message: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.ERROR)) {
            Timber.e(t, message, *args)
        }
    }

    // ========== 增强的日志方法（带格式化功能） ==========

    /**
     * 带格式化的Debug日志
     * @param format 格式化字符串
     * @param args 参数
     */
    fun df(format: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            try {
                val message = String.format(java.util.Locale.getDefault(), format, *args)
                Timber.d(message)
            } catch (e: Exception) {
                Timber.d("格式化日志失败: $format, 参数: ${args.joinToString()}")
            }
        }
    }

    /**
     * 带格式化的Info日志
     */
    fun inf(format: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            try {
                val message = String.format(java.util.Locale.getDefault(), format, *args)
                Timber.i(message)
            } catch (e: Exception) {
                Timber.i("格式化日志失败: $format, 参数: ${args.joinToString()}")
            }
        }
    }

    /**
     * 带格式化的Warning日志
     */
    fun wf(format: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.WARN)) {
            try {
                val message = String.format(java.util.Locale.getDefault(), format, *args)
                Timber.w(message)
            } catch (e: Exception) {
                Timber.w("格式化日志失败: $format, 参数: ${args.joinToString()}")
            }
        }
    }

    /**
     * 带格式化的Error日志
     */
    fun ef(format: String, vararg args: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.ERROR)) {
            try {
                val message = String.format(java.util.Locale.getDefault(), format, *args)
                Timber.e(message)
            } catch (e: Exception) {
                Timber.e("格式化日志失败: $format, 参数: ${args.joinToString()}")
            }
        }
    }

    // ========== 结构化日志方法 ==========

    /**
     * 方法进入日志
     * @param methodName 方法名
     * @param params 参数
     */
    fun enter(methodName: String, vararg params: Any?) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            val paramStr = if (params.isNotEmpty()) {
                " 参数: ${params.joinToString { it.toString() }}"
            } else {
                ""
            }
            Timber.d("➤ 进入方法: $methodName$paramStr")
        }
    }

    /**
     * 方法退出日志
     * @param methodName 方法名
     * @param result 返回值
     */
    fun exit(methodName: String, result: Any? = null) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            val resultStr = if (result != null) {
                " 返回: $result"
            } else {
                ""
            }
            Timber.d("◀ 退出方法: $methodName$resultStr")
        }
    }

    /**
     * 性能计时日志
     * @param tag 标识
     * @param duration 耗时（毫秒）
     */
    fun perf(tag: String, duration: Long) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            Timber.i("⏱ 性能统计 [$tag]: ${duration}ms")
        }
    }

    /**
     * 网络请求日志
     * @param method HTTP方法
     * @param url URL
     * @param statusCode 状态码
     * @param duration 耗时
     */
    fun net(method: String, url: String, statusCode: Int, duration: Long) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            Timber.i("🌐 网络请求 [$method] $url → $statusCode (${duration}ms)")
        }
    }

    /**
     * 数据库操作日志
     * @param operation 操作类型
     * @param table 表名
     * @param count 影响行数
     * @param duration 耗时
     */
    fun db(operation: String, table: String, count: Int = -1, duration: Long = -1) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            val countStr = if (count >= 0) " 影响行数: $count" else ""
            val durationStr = if (duration >= 0) " 耗时: ${duration}ms" else ""
            Timber.d("🗄 数据库操作 [$operation] $table$countStr$durationStr")
        }
    }

    /**
     * UI事件日志
     * @param event 事件类型
     * @param component 组件名
     * @param extra 额外信息
     */
    fun ui(event: String, component: String, extra: String? = null) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.DEBUG)) {
            val extraStr = if (extra != null) " ($extra)" else ""
            Timber.d("🎨 UI事件 [$event] $component$extraStr")
        }
    }

    /**
     * 业务逻辑日志
     * @param module 模块名
     * @param action 操作
     * @param result 结果
     */
    fun biz(module: String, action: String, result: String? = null) {
        if (LogConfig.shouldLog(currentLogLevel, LogConfig.LogLevel.INFO)) {
            val resultStr = if (result != null) " → $result" else ""
            Timber.i("💼 业务逻辑 [$module] $action$resultStr")
        }
    }

    /**
     * 带标签的日志输出
     */
    fun tag(tag: String): Timber.Tree {
        return Timber.tag(tag)
    }

    /**
     * 清理日志资源
     */
    fun cleanup() {
        Timber.i("LogManager正在清理资源")
        // 这里可以添加清理逻辑，如关闭文件流等
        isInitialized = false
    }

    /**
     * 判断是否为调试构建（备用方案）
     */
    private fun isDebugBuild(context: Context): Boolean {
        return try {
            // 首先尝试使用BuildConfig
            try {
                val buildConfigClass = Class.forName("${context.packageName}.BuildConfig")
                val debugField = buildConfigClass.getField("DEBUG")
                debugField.getBoolean(null)
            } catch (e: Exception) {
                // 备用方案：通过ApplicationInfo判断
                val applicationInfo = context.applicationInfo
                (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
            }
        } catch (e: Exception) {
            // 最后的备用方案：默认为false（生产模式）
            false
        }
    }
}

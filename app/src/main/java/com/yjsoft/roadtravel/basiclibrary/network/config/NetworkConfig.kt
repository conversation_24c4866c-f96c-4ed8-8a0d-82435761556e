package com.yjsoft.roadtravel.basiclibrary.network.config

import java.util.concurrent.TimeUnit

/**
 * 网络配置管理类
 */
object NetworkConfig {

    // 当前环境配置
    private var currentEnvironment: Environment = Environment.getDefault(isDebugBuild())
    
    // 超时配置（秒）
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // 缓存配置
    const val CACHE_SIZE = 50 * 1024 * 1024L // 50MB
    const val CACHE_MAX_AGE = 60 * 5 // 5分钟
    const val CACHE_MAX_STALE = 60 * 60 * 24 * 7 // 7天
    
    // 重试配置
    const val MAX_RETRY_COUNT = 3
    const val RETRY_DELAY_MS = 1000L
    const val RETRY_MAX_DELAY_MS = 10000L
    const val RETRY_MULTIPLIER = 2.0
    
    // 请求头配置
    const val HEADER_USER_AGENT = "User-Agent"
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_ACCEPT = "Accept"
    const val HEADER_CACHE_CONTROL = "Cache-Control"
    
    // 默认请求头值
    const val DEFAULT_USER_AGENT = "Android-App/1.0"
    const val DEFAULT_CONTENT_TYPE = "application/json"
    const val DEFAULT_ACCEPT = "application/json"
    
    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): Environment = currentEnvironment
    
    /**
     * 设置当前环境
     */
    fun setEnvironment(environment: Environment) {
        currentEnvironment = environment
    }
    
    /**
     * 获取当前BASE_URL
     */
    fun getBaseUrl(): String = currentEnvironment.baseUrl
    
    /**
     * 判断是否为调试模式
     */
    fun isDebugMode(): Boolean = currentEnvironment.isDebug
    
    /**
     * 获取连接超时时间
     */
    fun getConnectTimeout(): Long = CONNECT_TIMEOUT
    
    /**
     * 获取读取超时时间
     */
    fun getReadTimeout(): Long = READ_TIMEOUT
    
    /**
     * 获取写入超时时间
     */
    fun getWriteTimeout(): Long = WRITE_TIMEOUT
    
    /**
     * 获取超时时间单位
     */
    fun getTimeoutUnit(): TimeUnit = TimeUnit.SECONDS
    
    /**
     * 获取缓存大小
     */
    fun getCacheSize(): Long = CACHE_SIZE
    
    /**
     * 获取缓存最大存活时间
     */
    fun getCacheMaxAge(): Int = CACHE_MAX_AGE
    
    /**
     * 获取缓存最大过期时间
     */
    fun getCacheMaxStale(): Int = CACHE_MAX_STALE
    
    /**
     * 获取最大重试次数
     */
    fun getMaxRetryCount(): Int = MAX_RETRY_COUNT
    
    /**
     * 获取重试延迟时间
     */
    fun getRetryDelay(): Long = RETRY_DELAY_MS
    
    /**
     * 获取重试最大延迟时间
     */
    fun getRetryMaxDelay(): Long = RETRY_MAX_DELAY_MS
    
    /**
     * 获取重试延迟倍数
     */
    fun getRetryMultiplier(): Double = RETRY_MULTIPLIER
    
    /**
     * 获取默认请求头
     */
    fun getDefaultHeaders(): Map<String, String> {
        return mapOf(
            HEADER_USER_AGENT to DEFAULT_USER_AGENT,
            HEADER_CONTENT_TYPE to DEFAULT_CONTENT_TYPE,
            HEADER_ACCEPT to DEFAULT_ACCEPT
        )
    }
    
    /**
     * 环境切换监听器
     */
    interface EnvironmentChangeListener {
        fun onEnvironmentChanged(oldEnvironment: Environment, newEnvironment: Environment)
    }
    
    private val environmentChangeListeners = mutableListOf<EnvironmentChangeListener>()
    
    /**
     * 添加环境切换监听器
     */
    fun addEnvironmentChangeListener(listener: EnvironmentChangeListener) {
        environmentChangeListeners.add(listener)
    }
    
    /**
     * 移除环境切换监听器
     */
    fun removeEnvironmentChangeListener(listener: EnvironmentChangeListener) {
        environmentChangeListeners.remove(listener)
    }
    
    /**
     * 通知环境切换
     */
    private fun notifyEnvironmentChanged(oldEnvironment: Environment, newEnvironment: Environment) {
        environmentChangeListeners.forEach { listener ->
            try {
                listener.onEnvironmentChanged(oldEnvironment, newEnvironment)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 切换环境并通知监听器
     */
    fun switchEnvironment(environment: Environment) {
        val oldEnvironment = currentEnvironment
        currentEnvironment = environment
        notifyEnvironmentChanged(oldEnvironment, environment)
    }

    /**
     * 判断是否为调试构建
     * 这里使用一个简单的方法来判断，避免依赖BuildConfig
     */
    private fun isDebugBuild(): Boolean {
        // 方法1：通过系统属性判断
        return try {
            val debuggable = Class.forName("android.os.Build")
                .getDeclaredField("DEBUG")
                .getBoolean(null)
            debuggable
        } catch (e: Exception) {
            // 方法2：通过包名判断（开发环境通常包含debug字样）
            try {
                val packageName = System.getProperty("java.class.path") ?: ""
                packageName.contains("debug") || packageName.contains("Debug")
            } catch (e2: Exception) {
                // 默认返回true，确保开发时有详细日志
                true
            }
        }
    }
}

package com.yjsoft.roadtravel.basiclibrary.network

import com.yjsoft.roadtravel.ui.activities.login.models.LoginResponse
import com.yjsoft.roadtravel.ui.fragments.home.HomeApiResponse
import com.yjsoft.roadtravel.ui.fragments.home.GeoApiResponse
import com.yjsoft.roadtravel.ui.fragments.home.PlansApiResponse
import com.yjsoft.roadtravel.ui.activities.main.InitApiResponse
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Body
import retrofit2.http.Query

interface ApiService {
    // 初始化接口
    @GET("api/v1/front/init")
    suspend fun getInit(): InitApiResponse
    
    // 首页接口
    @GET("api/v1/front/index")
    suspend fun getHomeIndex(): HomeApiResponse
    
    // 地理信息接口
    @GET("api/v1/front/geo")
    suspend fun getGeoInfo(
        @Query("query_lat") lat: Double,
        @Query("query_lng") lng: Double
    ): GeoApiResponse
    
    // 计划列表接口
    @GET("api/v1/front/plans")
    suspend fun getPlans(
        @Query("hot_tag") hotTag: String
    ): PlansApiResponse

    @POST("api/v1/front/login")
    suspend fun login(
        @Query("code") code: String
    ): LoginResponse
}

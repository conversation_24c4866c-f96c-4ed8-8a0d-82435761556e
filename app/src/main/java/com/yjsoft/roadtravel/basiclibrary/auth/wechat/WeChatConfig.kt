package com.yjsoft.roadtravel.basiclibrary.auth.wechat

/**
 * 微信登录配置
 * 统一管理微信相关的配置参数
 */
object WeChatConfig {

    /**
     * 微信AppId
     */
    const val APP_ID = "wxd21ec061d3488b13"
    
    /**
     * 微信AppSecret
     
     * 注意：在生产环境中，AppSecret应该存储在服务器端，不应该暴露在客户端代码中
     */
    const val APP_SECRET = "09943ad76397bb6171cd8b1f2797f9b9"
    
    /**
     * 微信API基础URL
     */
    const val API_BASE_URL = "https://api.weixin.qq.com/"
    
    /**
     * 授权作用域
     */
    const val SCOPE = "snsapi_userinfo"
    
    /**
     * 检查配置是否有效
     */
    fun isConfigValid(): Boolean {
        return APP_ID != "wxd21ec061d3488b13" &&
               APP_SECRET != "09943ad76397bb6171cd8b1f2797f9b9" &&
               APP_ID.isNotBlank() &&
               APP_SECRET.isNotBlank() &&
               APP_ID.startsWith("wx")
    }
    
    /**
     * 获取配置状态描述
     */
    fun getConfigStatus(): String {
        return if (isConfigValid()) {
            "微信配置已设置"
        } else {
            "微信配置未设置，请配置真实的AppId和AppSecret"
        }
    }
}

package com.yjsoft.roadtravel.basiclibrary.network.models

import com.google.gson.annotations.SerializedName


/**
 * 统一的API响应包装类
 * @param T 响应数据的类型
 */
data class ApiResponse<T>(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("msg")
    val message: String,
    
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == SUCCESS_CODE
    
    /**
     * 判断请求是否失败
     */
    fun isFailure(): Boolean = !isSuccess()
    
    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): T? = if (isSuccess()) data else null
    
    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): T {
        return if (isSuccess()) {
            data ?: throw ApiException(code, "数据为空")
        } else {
            throw ApiException(code, message)
        }
    }
    
    companion object {
        const val SUCCESS_CODE = 0
        const val UNAUTHORIZED_CODE = 401
        const val FORBIDDEN_CODE = 403
        const val NOT_FOUND_CODE = 404
        const val SERVER_ERROR_CODE = 500
        const val TOKEN_EXPIRED_CODE = 900000
        
        /**
         * 创建成功响应
         */
        fun <T> success(data: T, message: String = "操作成功"): ApiResponse<T> {
            return ApiResponse(SUCCESS_CODE, message, data)
        }
        
        /**
         * 创建失败响应
         */
        fun <T> failure(code: Int, message: String): ApiResponse<T> {
            return ApiResponse(code, message, null)
        }
        
        /**
         * 创建服务器错误响应
         */
        fun <T> serverError(message: String = "服务器内部错误"): ApiResponse<T> {
            return ApiResponse(SERVER_ERROR_CODE, message, null)
        }
        
        /**
         * 创建未授权响应
         */
        fun <T> unauthorized(message: String = "未授权访问"): ApiResponse<T> {
            return ApiResponse(UNAUTHORIZED_CODE, message, null)
        }
    }
}

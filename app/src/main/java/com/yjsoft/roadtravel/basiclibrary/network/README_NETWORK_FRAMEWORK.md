# Android 网络请求框架使用指南

## 概述

这是一个基于 Retrofit + OkHttp 构建的企业级网络请求框架，集成了认证、错误处理、缓存、重试、日志等功能。

## 主要特性

### 🔐 认证管理
- 自动添加 Token 到请求头
- Token 自动刷新机制
- 认证失败自动处理

### 🛡️ 错误处理
- 统一的错误处理机制
- 网络错误、HTTP错误、业务错误分类处理
- 详细的错误信息和状态码

### 💾 智能缓存
- 内存缓存 + 磁盘缓存
- 多种缓存策略支持
- 缓存统计和管理

### 🔄 重试机制
- 指数退避重试策略
- 可配置的重试次数和延迟
- 智能异常判断

### 📝 日志记录
- 根据构建类型智能调整日志级别
- 请求ID追踪
- 详细的请求响应日志

### 🌐 网络状态监听
- 实时网络状态监听
- 网络类型检测
- 网络状态变化回调

## 快速开始

### 1. 基础配置

```kotlin
// 在 Application 或主 Activity 中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 设置环境
        NetworkConfig.setEnvironment(Environment.DEVELOPMENT)
        
        // 创建 Token 提供者
        val tokenProvider = object : TokenProvider {
            override suspend fun refreshToken(refreshToken: String): TokenInfo? {
                // 实现 Token 刷新逻辑
                return try {
                    val response = RetrofitInstance.getInstance(this@MyApplication).api
                        .refreshToken(RefreshTokenRequest(refreshToken))
                    if (response.isSuccess()) {
                        val tokenResponse = response.getDataOrThrow()
                        TokenInfo(
                            accessToken = tokenResponse.token,
                            refreshToken = tokenResponse.refreshToken
                        )
                    } else null
                } catch (e: Exception) {
                    null
                }
            }
            
            override fun onAuthFailure() {
                // 处理认证失败，跳转到登录页
            }
        }
        
        // 初始化 Retrofit 实例
        RetrofitInstance.getInstance(this, tokenProvider)
    }
}
```

### 2. 定义 API 接口

```kotlin
interface ApiService {
    @GET("users/{id}")
    suspend fun getUser(@Path("id") userId: String): ApiResponse<User>
    
    @GET("users")
    suspend fun getUsers(@Query("page") page: Int = 1): ApiResponse<List<User>>
    
    @POST("auth/login")
    suspend fun login(@Body loginRequest: LoginRequest): ApiResponse<LoginResponse>
}
```

### 3. 在 ViewModel 中使用

```kotlin
class UserViewModel(private val context: Context) : ViewModel() {
    
    private val apiService = RetrofitInstance.getInstance(context).api
    
    private val _userState = MutableStateFlow<NetworkResult<User>>(NetworkResult.loading())
    val userState: StateFlow<NetworkResult<User>> = _userState.asStateFlow()
    
    fun getUser(userId: String) {
        viewModelScope.launch {
            _userState.value = NetworkResult.loading()
            
            try {
                val response = apiService.getUser(userId)
                _userState.value = NetworkResult.fromApiResponse(response)
            } catch (e: Exception) {
                _userState.value = NetworkResult.error(
                    e as? ApiException ?: ApiException(-1, e.message ?: "未知错误")
                )
            }
        }
    }
}
```

### 4. 在 Compose UI 中使用

```kotlin
@Composable
fun UserScreen(viewModel: UserViewModel) {
    val userState by viewModel.userState.collectAsState()
    
    when (userState) {
        is NetworkResult.Loading -> {
            CircularProgressIndicator()
        }
        is NetworkResult.Success -> {
            val user = userState.data
            Text("用户名: ${user.name}")
        }
        is NetworkResult.Error -> {
            val error = userState.error
            Text("错误: ${error.message}")
        }
    }
}
```

## 高级功能

### 重试机制

```kotlin
// 使用默认重试策略
val response = CoroutineRetryUtils.retry {
    apiService.getUsers()
}

// 使用自定义重试策略
val response = CoroutineRetryUtils.retryWithPolicy(
    RetryPolicy.aggressive()
) {
    apiService.getUsers()
}
```

### 请求去重

```kotlin
// 避免重复请求
val cacheKey = NetworkUtils.generateCacheKey("/users/123")
val response = NetworkUtils.deduplicateRequest(cacheKey) {
    apiService.getUser("123")
}
```

### 网络状态监听

```kotlin
// 监听网络状态变化
NetworkUtils.observeNetworkState(context).collect { state ->
    if (state.isConnected) {
        println("网络已连接: ${state.type}")
    } else {
        println("网络已断开")
    }
}
```

### 缓存管理

```kotlin
// 获取缓存统计
val networkCache = RetrofitInstance.getInstance(context).getNetworkCache()
val stats = networkCache.getCacheStats()
println("缓存命中率: ${stats.getHitRate()}")
println("缓存大小: ${stats.getFormattedSize()}")

// 清除缓存
networkCache.clearCache()
```

## 环境配置

支持多环境配置：

```kotlin
// 切换到测试环境
NetworkConfig.setEnvironment(Environment.STAGING)

// 切换到生产环境
NetworkConfig.setEnvironment(Environment.PRODUCTION)
```

## 错误处理

框架提供了完整的错误处理机制：

```kotlin
// 处理不同类型的错误
when (error.code) {
    ApiError.NETWORK_ERROR -> {
        // 网络错误处理
    }
    ApiError.TIMEOUT_ERROR -> {
        // 超时错误处理
    }
    ApiResponse.UNAUTHORIZED_CODE -> {
        // 未授权错误处理
    }
    ApiResponse.TOKEN_EXPIRED_CODE -> {
        // Token过期处理
    }
}
```

## 最佳实践

### 1. Repository 模式

```kotlin
class UserRepository(private val context: Context) {
    private val apiService = RetrofitInstance.getInstance(context).api
    
    suspend fun getUser(userId: String): NetworkResult<User> {
        return try {
            val response = apiService.getUser(userId)
            NetworkResult.fromApiResponse(response)
        } catch (e: Exception) {
            NetworkResult.error(e as? ApiException ?: ApiException(-1, e.message ?: "未知错误"))
        }
    }
}
```

### 2. 统一的状态管理

```kotlin
// 使用 NetworkResult 统一管理请求状态
sealed class NetworkResult<out T> {
    object Loading : NetworkResult<Nothing>()
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error(val error: ApiError) : NetworkResult<Nothing>()
}
```

### 3. 错误处理

```kotlin
// 统一的错误处理
fun handleError(error: ApiError) {
    when {
        error.isNetworkError() -> showToast("网络连接失败")
        error.isTimeoutError() -> showToast("请求超时")
        error.isAuthError() -> navigateToLogin()
        error.isServerError() -> showToast("服务器错误")
        else -> showToast(error.message)
    }
}
```

## 注意事项

1. **内存管理**: 在 Activity/Fragment 销毁时记得取消协程
2. **网络权限**: 确保在 AndroidManifest.xml 中添加网络权限
3. **混淆配置**: 添加相应的混淆规则保护网络相关类
4. **测试**: 建议为网络请求编写单元测试和集成测试

## 依赖项

确保在 `build.gradle.kts` 中添加以下依赖：

```kotlin
implementation(libs.retrofit.core)
implementation(libs.retrofit.converter.gson)
implementation(libs.okhttp.logging.interceptor)
implementation(libs.gson)
implementation(libs.kotlinx.coroutines.core)
implementation(libs.kotlinx.coroutines.android)
implementation(libs.androidx.lifecycle.viewmodel.ktx)
```

## 更新日志

### v1.0.0
- 初始版本发布
- 集成认证、错误处理、缓存、重试、日志功能
- 支持多环境配置
- 提供完整的使用示例和文档

package com.yjsoft.roadtravel.basiclibrary.auth

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatApiService
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatConfig
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatTokenResult
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenInfo
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 微信Token提供者
 * 
 * 实现TokenProvider接口，提供微信登录的token刷新功能
 * 负责处理微信access_token的刷新和认证失败的处理
 * 
 * 功能：
 * - 微信token刷新
 * - 认证失败处理
 * - 错误日志记录
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
class WeChatTokenProvider(
    private val context: Context
) : TokenProvider {
    
    private val TAG = "WeChatTokenProvider"
    
    // 微信API服务
    private val weChatApiService: WeChatApiService by lazy {
        // 创建Retrofit实例并生成API服务
        retrofit2.Retrofit.Builder()
            .baseUrl(WeChatConfig.API_BASE_URL)
            .addConverterFactory(retrofit2.converter.gson.GsonConverterFactory.create())
            .build()
            .create(WeChatApiService::class.java)
    }
    
    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新Token
     * @return 新的Token信息，失败时返回null
     */
    override suspend fun refreshToken(refreshToken: String): TokenInfo? {
        return withContext(Dispatchers.IO) {
            try {
                LogManager.d(TAG, "开始刷新微信Token")
                
                // 调用微信API刷新token
                val response = weChatApiService.refreshAccessToken(
                    appId = WeChatConfig.APP_ID,
                    grantType = "refresh_token",
                    refreshToken = refreshToken
                )
                
                if (response.isSuccessful) {
                    val body = response.body()
                    if (body != null && body.isSuccess()) {
                        // 刷新成功，保存新的token
                        val newAccessToken = body.access_token ?: ""
                        val newRefreshToken = body.refresh_token ?: ""
                        val expiresIn = body.expires_in ?: 7200
                        
                        if (newAccessToken.isNotEmpty()) {
                            // 保存到TokenManager
                            TokenManager.updateTokens(newAccessToken, newRefreshToken, expiresIn)
                            
                            LogManager.d(TAG, "微信Token刷新成功")
                            
                            TokenInfo(
                                accessToken = newAccessToken,
                                refreshToken = newRefreshToken,
                                expiresIn = expiresIn.toLong()
                            )
                        } else {
                            LogManager.w(TAG, "微信Token刷新返回的access_token为空")
                            null
                        }
                    } else {
                        LogManager.w(TAG, "微信Token刷新失败: ${body?.getErrorMessage()}")
                        null
                    }
                } else {
                    LogManager.w(TAG, "微信Token刷新网络请求失败: ${response.code()}")
                    null
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "微信Token刷新异常", e)
                null
            }
        }
    }
    
    /**
     * 认证失败回调
     * 
     * 当token刷新失败或认证失败时调用
     * 负责清除本地认证信息并跳转到登录页
     */
    @OptIn(DelicateCoroutinesApi::class)
    override fun onAuthFailure() {
        try {
            LogManager.w(TAG, "微信认证失败，清除本地认证信息")
            
            // 清除本地token信息
            // 注意：这里不能直接调用suspend函数，需要在协程中执行
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    TokenManager.clearTokens()
                    LogManager.d(TAG, "本地认证信息清除成功")
                } catch (e: Exception) {
                    LogManager.e(TAG, "清除本地认证信息失败", e)
                }
            }
            
            // TODO: 跳转到登录页面
            // 这里应该通过某种方式通知UI层跳转到登录页
            // 可以考虑使用EventBus、LiveData或其他事件机制
            LogManager.w(TAG, "需要跳转到登录页面")
            
        } catch (e: Exception) {
            LogManager.e(TAG, "处理认证失败异常", e)
        }
    }
    
    /**
     * 检查当前token是否有效
     * 
     * @return true表示token有效，false表示需要刷新或重新登录
     */
    fun isTokenValid(): Boolean {
        return try {
            if (!TokenManager.isInitialized()) {
                TokenManager.initialize(context)
            }
            
            val accessToken = TokenManager.getAccessToken()
            val isExpired = TokenManager.isTokenExpired()
            
            accessToken.isNotEmpty() && !isExpired
        } catch (e: Exception) {
            LogManager.e(TAG, "检查token有效性失败", e)
            false
        }
    }
    
    /**
     * 获取当前token摘要信息（用于调试）
     */
    fun getTokenSummary(): String {
        return try {
            if (!TokenManager.isInitialized()) {
                TokenManager.initialize(context)
            }
            
            TokenManager.getTokenSummary()
        } catch (e: Exception) {
            "获取token摘要失败: ${e.message}"
        }
    }
} 
package com.yjsoft.roadtravel.application

import android.app.Application
import android.content.pm.ApplicationInfo
import android.os.Build
import androidx.annotation.RequiresApi
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.di.HiltConfig
import com.yjsoft.roadtravel.basiclibrary.di.HiltDiagnostics
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogUtils
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance
import com.yjsoft.roadtravel.basiclibrary.auth.WeChatTokenProvider
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatConfig
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import javax.inject.Inject


/**
 * 应用程序类
 * 负责应用的全局初始化工作
 *
 * 使用Hilt进行依赖注入，同时保持向后兼容性
 */
@HiltAndroidApp
class RoadTravelApplication : Application() {

    // Hilt配置（可选注入，兼容模式）
    @Inject
    lateinit var hiltConfig: HiltConfig

    @RequiresApi(Build.VERSION_CODES.P)
    override fun onCreate() {
        super.onCreate()

        // 先初始化日志框架
        initializeLogging()

        // 然后初始化Hilt配置（如果可用）
        initializeHiltConfig()

        // 运行Hilt诊断（调试模式下）
        if (isDebugBuild()) {
            HiltDiagnostics.runDiagnostics(this)
        }

        // 初始化网络框架
        initializeNetworking()

        // 初始化权限框架
        initializePermissionFramework()

        // 初始化DataStore框架
        initializeDataStore()

        // 初始化图片加载框架
        initializeImageFramework()

        // 初始化支付框架
        initializePaymentFramework()


        // 记录应用启动日志
        LogManager.i("应用启动完成")
        LogManager.d("应用信息 - 包名: $packageName, 版本: ${getVersionInfo()}")

        // 创建日志目录
        LogUtils.createLogDirectories(this)

        // 清理过期日志（保留7天）
        LogUtils.clearLogsOlderThan(this, 7)

        // 记录日志统计信息
        val stats = LogUtils.getLogStatistics(this)
        LogManager.d(
            "日志统计 - 日志文件: ${stats.totalLogFiles}, 崩溃文件: ${stats.totalCrashFiles}, 总大小: ${
                LogUtils.formatFileSize(
                    stats.totalLogSize
                )
            }"
        )
    }

    /**
     * 初始化Hilt配置
     */
    private fun initializeHiltConfig() {
        try {
            LogManager.w("🔍 [RoadTravelApp] 开始检查Hilt依赖注入状态")

            // 检查Hilt是否可用（兼容模式）
            if (::hiltConfig.isInitialized) {
                LogManager.w("✅ [RoadTravelApp] Hilt依赖注入可用，开始初始化")
                hiltConfig.initialize()
                LogManager.w("🎉 [RoadTravelApp] Hilt依赖注入框架初始化成功")

                // 输出配置信息
                val configInfo = hiltConfig.getConfigInfo()
                LogManager.w("📋 [RoadTravelApp] Hilt配置信息: %s", configInfo)
            } else {
                LogManager.w("❌ [RoadTravelApp] Hilt未注入，可能的原因:")
                LogManager.w("1. @HiltAndroidApp注解未生效")
                LogManager.w("2. Hilt编译器未正确处理")
                LogManager.w("3. kapt配置问题")
                LogManager.w("4. 需要Clean Project并重新编译")
                LogManager.w("🔄 [RoadTravelApp] 使用手动初始化模式")
            }
        } catch (e: Exception) {
            // Hilt初始化失败，回退到手动初始化
            LogManager.e(e, "💥 [RoadTravelApp] Hilt初始化失败，回退到手动初始化")
        }
    }

    /**
     * 初始化日志框架
     */
    private fun initializeLogging() {
        try {
            LogManager.init(this)

            // 在调试模式下输出初始化成功信息
            val isDebug = isDebugBuild()
            if (isDebug) {
                LogManager.d("日志框架初始化成功")
                LogManager.d("当前日志级别: ${LogManager.getCurrentLogLevel().name}")
                LogManager.setStandardMode(showDetailedTag = true)
            }

        } catch (e: Exception) {
            // 如果日志框架初始化失败，使用系统日志输出错误
            Timber.Forest.tag("RoadTravelApp").e(e, "日志框架初始化失败")
        }
    }

    /**
     * 初始化网络框架
     */
    private fun initializeNetworking() {
        try {
            // 初始化TokenManager
            com.yjsoft.roadtravel.basiclibrary.auth.TokenManager.initialize(this)
            LogManager.d("TokenManager初始化成功")
            
            // 创建微信Token提供者
            val weChatTokenProvider = WeChatTokenProvider(this)
            LogManager.d("WeChatTokenProvider创建成功")
            
            // 初始化Retrofit实例，集成TokenProvider
            RetrofitInstance.getInstance(this, weChatTokenProvider)
            LogManager.d("RetrofitInstance初始化成功")
            
            // 初始化网络框架，使用默认配置
            NetworkManager.init(this)
            LogManager.d("网络框架初始化成功")
            
            // 记录网络框架状态
            val status = NetworkManager.getStatus()
            LogManager.d("网络框架状态 - 环境: ${status.currentEnvironment?.name}, BaseURL: ${status.baseUrl}")
            
            // 记录Token状态
            val tokenSummary = weChatTokenProvider.getTokenSummary()
            LogManager.d("Token状态: $tokenSummary")

        } catch (e: Exception) {
            LogManager.e(e, "网络框架初始化失败")
        }
    }

    /**
     * 初始化权限框架
     */
    private fun initializePermissionFramework() {
        try {
            // 初始化权限框架默认配置
            PermissionConfig.initializeDefaults()

            LogManager.d("权限框架初始化成功")
            LogManager.d("权限框架配置 - 默认策略: ${PermissionConfig.defaultRequestStrategy.name}")

        } catch (e: Exception) {
            LogManager.e(e, "权限框架初始化失败")
        }
    }

    /**
     * 初始化DataStore框架
     */
    private fun initializeDataStore() {
        try {
            // 初始化DataStore管理器
            DataStoreManager.init(this)

            LogManager.d("DataStore框架初始化成功")

        } catch (e: Exception) {
            LogManager.e(e, "DataStore框架初始化失败")
        }
    }

    /**
     * 初始化图片加载框架
     */
    private fun initializeImageFramework() {
        try {
            // 根据构建类型选择配置
            val isDebug = isDebugBuild()

            // 初始化图片加载框架
            if (isDebug) {
                // 调试环境：启用详细日志和调试功能
                ImageManager.init(this)
                LogManager.d("图片加载框架初始化成功（调试模式）")
            } else {
                // 发布环境：使用性能优化配置
                ImageManager.init(this)
                LogManager.d("图片加载框架初始化成功（发布模式）")
            }

            // 记录图片框架状态
            val isInitialized = ImageManager.isInitialized()
            LogManager.d("图片框架状态 - 已初始化: $isInitialized")

        } catch (e: Exception) {
            LogManager.e(e, "图片加载框架初始化失败")
        }
    }

    /**
     * 初始化支付框架
     */
    private fun initializePaymentFramework() {
        try {
            // 根据构建类型选择配置
            val isDebug = isDebugBuild()

            // 初始化支付管理器
            PaymentManager.init(this, debugMode = isDebug)

            // 配置支付参数（实际使用时需要替换为真实参数）
            if (isDebug) {
                // 测试环境配置
                configureTestPaymentParams()
                LogManager.d("支付框架初始化成功（测试模式）")
            } else {
                // 生产环境配置
                configureProductionPaymentParams()
                LogManager.d("支付框架初始化成功（生产模式）")
            }

            // 记录支付框架状态
            val enabledTypes = PaymentConfig.getEnabledPaymentTypes()
            LogManager.d("支付框架状态 - 已启用支付方式: ${enabledTypes.map { it.name }}")

        } catch (e: Exception) {
            LogManager.e(e, "支付框架初始化失败")
        }
    }

    /**
     * 配置测试环境支付参数
     */
    private fun configureTestPaymentParams() {
        // 配置支付宝测试参数（示例参数，实际使用时需要替换）
        PaymentConfig.configureAlipay(
            appId = "2021000000000000", // 测试AppId
            privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...", // 测试私钥
            publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA..." // 测试公钥
        )

        // 配置微信支付测试参数（示例参数，实际使用时需要替换）
        PaymentConfig.configureWeChatPay(
            appId = "wx1234567890abcdef", // 测试AppId
            merchantId = "1234567890", // 测试商户号
            apiKey = "abcdefghijklmnopqrstuvwxyz123456" // 测试API密钥
        )

        // 设置较短的支付超时时间用于测试
        PaymentConfig.setPaymentTimeout(15_000L)
    }

    /**
     * 配置生产环境支付参数
     */
    private fun configureProductionPaymentParams() {
        // TODO: 配置生产环境的真实支付参数
        // 注意：实际的支付参数应该从安全的配置文件或远程配置中获取
        // 不应该硬编码在代码中

        LogManager.w("生产环境支付参数未配置，请在实际部署前配置真实的支付参数")
    }

    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            val applicationInfo = applicationInfo
            (applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取版本信息
     */
    @RequiresApi(Build.VERSION_CODES.P)
    private fun getVersionInfo(): String {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            "${packageInfo.versionName} (${packageInfo.longVersionCode})"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    override fun onTerminate() {
        super.onTerminate()

        // 记录应用终止日志
        LogManager.i("应用正在终止")

        // 清理网络框架资源
        NetworkManager.cleanup()

        // 清理图片框架资源
        ImageManager.clearAllCache()

        // 清理支付框架资源
        PaymentManager.cleanup()

        // 清理日志资源
        LogManager.cleanup()
    }

    override fun onLowMemory() {
        super.onLowMemory()
        LogManager.w("系统内存不足警告")
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)

        val levelName = when (level) {
            TRIM_MEMORY_RUNNING_MODERATE -> "RUNNING_MODERATE"
            TRIM_MEMORY_RUNNING_LOW -> "RUNNING_LOW"
            TRIM_MEMORY_RUNNING_CRITICAL -> "RUNNING_CRITICAL"
            TRIM_MEMORY_UI_HIDDEN -> "UI_HIDDEN"
            TRIM_MEMORY_BACKGROUND -> "BACKGROUND"
            TRIM_MEMORY_MODERATE -> "MODERATE"
            TRIM_MEMORY_COMPLETE -> "COMPLETE"
            else -> "UNKNOWN($level)"
        }

        LogManager.w("内存修剪请求: $levelName")

        // 根据内存压力级别清理图片缓存
        when (level) {
            TRIM_MEMORY_RUNNING_MODERATE,
            TRIM_MEMORY_RUNNING_LOW -> {
                // 中等内存压力：清理内存缓存
                ImageManager.clearMemoryCache()
                LogManager.d("已清理图片内存缓存（内存压力: $levelName）")
            }

            TRIM_MEMORY_RUNNING_CRITICAL,
            TRIM_MEMORY_COMPLETE -> {
                // 严重内存压力：清理所有缓存
                ImageManager.clearAllCache()
                LogManager.d("已清理所有图片缓存（内存压力: $levelName）")
            }
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!--允许访问网络，必选权限-->
    <uses-permission android:name="android.permission.INTERNET" />

    <!--允许获取精确位置，精准定位必选-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!--允许获取粗略位置，粗略定位必选-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!--允许获取网络状态，用于网络定位（无gps情况下的定位），若需网络定位功能则必选-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!--允许获取wifi网络信息，用于网络定位（无gps情况下的定位），若需网络定位功能则必选-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!--允许获取wifi状态改变，用于网络定位（无gps情况下的定位），若需网络定位功能则必选-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!--后台获取位置信息，若需后台定位则必选-->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!--用于申请调用A-GPS模块,卫星定位加速-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />

    <!--允许写设备缓存，用于问题排查-->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />

    <!-- 支付相关权限 -->
    <!--微信支付SDK需要的权限-->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application
        android:name=".application.RoadTravelApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.RoadTravel"
        tools:targetApi="33">

        <!-- 高德授权 -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="5dad9f89e44ada3d55f85668c6e886f1" />
        <service android:name="com.amap.api.location.APSService" />
        <!-- 高德授权结束 -->

        <!-- 支付相关配置 -->
        <!-- 微信支付回调Activity -->
        <activity
            android:name=".basiclibrary.payment.strategies.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- 微信登录回调Activity -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.yjsoft.roadtravel"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- 支付演示Activity -->
        <activity
            android:name=".basiclibrary.payment.PaymentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RoadTravel">
            <intent-filter>
                <action android:name="com.yjsoft.roadtravel.action.PAYMENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 登录Activity -->
        <activity
            android:name=".ui.activities.login.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RoadTravel">
            <intent-filter>
                <action android:name="com.yjsoft.roadtravel.action.LOGIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- AI对话页面 -->
        <activity
            android:name=".ui.activities.aichat.AIChatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RoadTravel">
            <intent-filter>
                <action android:name="com.yjsoft.roadtravel.action.AICHAT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- WebView演示Activity -->
        <activity
            android:name=".basiclibrary.navigation.WebViewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RoadTravel">
            <intent-filter>
                <action android:name="com.yjsoft.roadtravel.action.WEBVIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activities.main.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RoadTravel">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- FileProvider for sharing log files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.yjsoft.roadtravel.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>
    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

</manifest>